using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Cinemachine;

public enum RoomDirection
{
    Up,
    Down,
    Left,
    Right
}

public class RoomManager : SingletonMono<RoomManager>
{
    [SerializeField] List<Room> roomList;
    public List<Room> RoomList => roomList;
    [SerializeField] Room currentRoom;
    [SerializeField] Transform cameraBounds;
    [SerializeField] Transform playerSpawnPoint;
    public Room CurrentRoom
    {
        get => currentRoom;
        set
        {
            // Guard: if the incoming reference is null or the object has been destroyed, ignore the assignment
            if (value == null)
            {
                Debug.LogWarning("RoomManager.CurrentRoom: attempted to set to null or destroyed Room. Assignment ignored.");
                return;
            }
            currentRoom = value;
            if (cameraBounds != null && currentRoom != null)
                cameraBounds.position = currentRoom.transform.position;

            Camera mainCamera = Camera.main;
            if (mainCamera != null && mainCamera.TryGetComponent<CinemachineBrain>(out var brain))
            {
                var vcam = brain.ActiveVirtualCamera as CinemachineVirtualCameraBase;
                if (vcam != null)
                {
                    vcam.PreviousStateIsValid = false;
                    vcam.UpdateCameraState(Vector3.up, 0f);
                    var state = vcam.State;
                    mainCamera.transform.SetPositionAndRotation(state.FinalPosition, state.FinalOrientation);
                }
            }
        }
    }
    readonly List<MarkData> markDatas = new();
    readonly List<MarkData> systemMarkDatas = new();
    public List<MarkData> MarkDatas => markDatas;
    public List<MarkData> SystemMarkDatas => systemMarkDatas;
    

    void Start()
    {
        GameEventSystem.AddListener(GameEventType.SceneLoaded, OnSceneLoaded);
    }

    void Update()
    {

    }

    void OnSceneLoaded()
    {
        InitRoomList();
    }

    // Expose a safe way for external systems (like SaveManager) to force-refresh the room list
    public void RefreshRoomList()
    {
        InitRoomList();
    }

    void InitRoomList()
    {
        roomList.Clear();
        Room[] rooms = FindObjectsOfType<Room>();
        roomList.AddRange(rooms);
        roomList.Sort((a, b) => a.index.CompareTo(b.index));

        currentRoom = roomList.Find(r => r.roomType == RoomType.Start);
        if (currentRoom != null)
        {
            GameEventSystem.Trigger(GameEventType.EnterRoom, currentRoom.roomType);
            if (playerSpawnPoint != null)
            {
                playerSpawnPoint.transform.position = currentRoom.transform.position;
            }
            PlayerController player = FindObjectOfType<PlayerController>();
            if (player != null)
            {
                player.transform.position = currentRoom.transform.position;
            }
            CurrentRoom = currentRoom;
        }

        for (int i = 0; i < roomList.Count; i++)
        {
            MarkData markData = new()
            {
                row = roomList[i].row,
                col = roomList[i].col,
                index = roomList[i].index,
                markType = MarkType.None,
            };
            markDatas.Add(markData);

            MarkType systemMarkType = MarkType.None;
            if (roomList[i].roomType == RoomType.Normal)
                systemMarkType = MarkType.Safe;
            else if (roomList[i].roomType == RoomType.Dead)
                systemMarkType = MarkType.Danger;
            else if (roomList[i].roomType == RoomType.Basement)
                systemMarkType = MarkType.Error;
            else if (roomList[i].roomType == RoomType.Luxury)
                systemMarkType = MarkType.Important;
            else if (roomList[i].roomType == RoomType.Start)
                systemMarkType = MarkType.Start;
            else if (roomList[i].roomType == RoomType.End)
                systemMarkType = MarkType.End;
            MarkData systemMarkData = new()
            {
                row = roomList[i].row,
                col = roomList[i].col,
                index = roomList[i].index,
                markType = systemMarkType,
            };
            systemMarkDatas.Add(systemMarkData);
        }
    }

    public Room GetNextRoom(RoomDirection direction)
    {
        if (currentRoom == null)
        {
            Debug.LogWarning("Current room is null");
            return null;
        }

        int row = currentRoom.row;
        int col = currentRoom.col;

        int step = 1;
        if (currentRoom.roomType == RoomType.Luxury) step = 2;

        switch (direction)
        {
            case RoomDirection.Up:
                row -= step;
                break;
            case RoomDirection.Down:
                row += step;
                break;
            case RoomDirection.Left:
                col -= step;
                break;
            case RoomDirection.Right:
                col += step;
                break;
        }

        Room nextRoom = roomList.Find(r => r.row == row && r.col == col);
        if (nextRoom != null)
        {
            return nextRoom;
        }
        return currentRoom;
    }

    void OnDestroy()
    {
        GameEventSystem.RemoveListener(GameEventType.SceneLoaded, OnSceneLoaded);
    }
}

[Serializable]
public class MarkData
{
    public int row;
    public int col;
    public int index;
    public MarkType markType;
}