using UnityEngine;

public class TestSave : MonoBehaviour
{
    void Update()
    {
        if (Input.GetKeyDown(KeyCode.P))
        {
            if (UIManager.Instance != null)
            {
                UIManager.Instance.ShowUI<UISavePanel>();
            }
            else
            {
                Debug.Log<PERSON>rror("UIManager.Instance is null.");
            }
        }
        if (Input.GetKeyDown(KeyCode.S))
        {
            if (SaveManager.Instance != null)
            {
                SaveManager.Instance.SaveGame(1);
            }
            else
            {
                Debug.Log<PERSON>rror("SaveManager.Instance is null.");
            }
        }
        if (Input.GetKeyDown(KeyCode.L))
        {
            if (SaveManager.Instance != null)
            {
                SaveManager.Instance.LoadGame(1, () => Debug.Log("加载完成"));
            }
            else
            {
                Debug.LogError("SaveManager.Instance is null.");
            }
        }
    }
    
}