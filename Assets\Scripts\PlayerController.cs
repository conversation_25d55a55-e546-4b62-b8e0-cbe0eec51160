﻿using UnityEngine;
using System.Collections;
using System;

public class PlayerController : MonoBehaviour
{
    [Header("移动参数")]
    [SerializeField] private float moveSpeed = 5f;
    [SerializeField] private float jumpForce = 10f;
    [SerializeField] private LayerMask groundLayer;
    [SerializeField] private Transform groundCheck;
    [SerializeField] private float groundCheckRadius = 0.2f;
    
    [Header("生命与重生")]
    public Transform respawnPoint;    // 死亡后重生位置
    public int maxHealth = 3;         // 最大生命值
    private int currentHealth;        // 当前生命值
    private bool isDead = false;      // 玩家是否死亡
    public int CurrentHealth     
    {
        get => currentHealth;
        set => currentHealth = value;
    }

    [Header("一些组件")]
    private Rigidbody2D rb;
    private SpriteRenderer playerSprite;
    [SerializeField] Animator playerAnimator;

    [Header("跳跃与输入")]
    public bool playerCanJump;
    private float horizontal;
    private bool isGrounded;
    private bool isInvertedControls = false;
    private bool wasKinematic;
    private Coroutine attachCoroutine;
    private Transform originalParent;

    [Header("特殊状态系统")]
    public bool hasShield = false;       // 是否拥有护盾
    public float san = 100f;        // san值
    private bool inBasement = false;     // 是否处于地下室
    public float sanDecreaseRate = 5f;   // 地下室每秒减少san的速度
    public bool hasKey = false;          // 是否拥有房卡
    public bool hasLetter = false;          // 是否拥有信件
    public bool ExtraMove = false; // 餐厅效果
    public bool LetterLost = false; // 暗室效果
    public bool InBasement
    {
        get => inBasement;
        set => inBasement = value;
    }

    [Header("控制锁定状态")]
    public bool isControllable = true;   // 是否允许玩家输入与移动
    public bool isInvincible = false;    // 是否无敌（例如传送中、剧情中）


    void Start()
    {
        rb = GetComponent<Rigidbody2D>();
        playerSprite = GetComponent<SpriteRenderer>();
        groundLayer = LayerMask.GetMask("Ground");

        if (respawnPoint == null)
            respawnPoint = this.transform; // 默认当前位置
        // 订阅房间进入事件，用于通过房间类型判断地下室/护盾逻辑（传送门进入时碰撞触发器可能不可用）
        try
        {
            GameEventSystem.AddListener<RoomType>(GameEventType.EnterRoom, OnEnterRoom);
        }
        catch (Exception)
        {
            Debug.LogWarning("PlayerController: unable to subscribe to EnterRoom event");
        }
    }

    void Update()
    {

        if (isDead || !isControllable || (TipsManager.Instance != null && TipsManager.Instance.isTips)) return; // 死亡后或对话中不处理输入

        // 检测输入
        horizontal = Input.GetAxisRaw("Horizontal");

        // 跳跃判断
        isGrounded = Physics2D.OverlapCircle(groundCheck.position, groundCheckRadius, groundLayer);
        if (isGrounded && Input.GetButtonDown("Jump") && playerCanJump)
        {
            rb.AddForce(Vector2.up * jumpForce, ForceMode2D.Impulse);
        }
        
        //地下室san值降低机制
        if (inBasement)
        {
            Debug.Log("san值 = " + san);
            san -= Time.deltaTime * sanDecreaseRate;
            //UIManager.Instance.UpdateSanUI(san); // 更新San值UI
            if (san <= 0)
                {
                    Debug.Log("san值为0，触发死亡");
                    san = 0;
                    Die();
                }
        }
    }

    void FixedUpdate()
    {
        if (isDead || !isControllable || (TipsManager.Instance != null && TipsManager.Instance.isTips)) return;  //死亡后或对话中不处理输入

        // 处理物理移动
        if (isInvertedControls) horizontal = -horizontal;
        rb.velocity = new Vector2(horizontal * moveSpeed, rb.velocity.y);

        // 切换动画并翻转
        playerAnimator.SetFloat("move", Mathf.Abs(horizontal));
        if (horizontal != 0)
        {
            playerAnimator.transform.localScale = new Vector3(horizontal > 0 ? 1 : -1, 1, 1);
        }
    }

    /// <summary>
    /// Player 受到伤害时调用
    /// </summary>
    /// <param name="dmg"></param>
    public void TakeDamage(int dmg)
    {
        if (isDead || isInvincible)  return;
        
            // 护盾抵消一次伤害
        if (hasShield)
        {
            Debug.Log("护盾抵消了一次伤害！");
            hasShield = false;
            return;
        }

        currentHealth -= dmg;
        GameEventSystem.Trigger(GameEventType.PlayerInjured);
        Debug.Log("玩家受到伤害" + dmg);
         
        if (currentHealth <= 0)
        {
            Debug.Log("TakeDamage内部判断玩家死亡");
            Die();
            GameEventSystem.Trigger(GameEventType.PlayerInjured);
        }
    }

    private void Die()
    {
        if (isDead) return;

        isDead = true;
        rb.velocity = Vector2.zero;
        rb.bodyType = RigidbodyType2D.Static; // 禁止物理移动
        Debug.Log("调用了Die，玩家无法移动和交互");


        // 延迟调用死亡处理（复活或游戏结束）
        Invoke(nameof(OnDeathComplete), 1.5f);
    }

    private void OnDeathComplete()
    {
        // 重生
        Respawn();

        // 或 显示GameOver界面
        Debug.Log("GameOver");
    }
    
    /// <summary>
    /// 移动玩家到重生点
    /// </summary>
    private void Respawn()
    {
        // 调用场景，移动玩家到第一关的重生点
        TransitionManager.Instance.TransitionToFirstScene();

        GameEventSystem.Trigger(GameEventType.PlayerRespawned);
    }


    /// <summary>
    /// 启用Player移动和生命恢复到最大值
    /// </summary>
    public void Revive()
    {
        isDead = false;
        
        // 恢复san值
        san = 100f;

        // 恢复生命
        currentHealth = maxHealth;

        // 启用移动
        rb.bodyType = RigidbodyType2D.Dynamic;

        hasKey = false;
        hasShield = false;
        hasLetter = true;
        ExtraMove = false;
        LetterLost = false;
        inBasement = false;
    }

    
    /// <summary>
    /// 当玩家进入碰撞区域时调用
    /// </summary>
    /// <param name="other">碰撞体名称，确保对方Collider设置为 Trigger</param>
     private void OnTriggerEnter2D(Collider2D other)
    {
        if (isDead || !isControllable) return;

        if (other.CompareTag("BasementTrigger"))
        {
            if(inBasement != true)
            {
                inBasement = true;
                Debug.Log("玩家进入地下室");
            }
        }

        if (other.CompareTag("LuxuryRoomTrigger"))
        {
            if(hasShield != true)
            {
                hasShield = true;
                Debug.Log("玩家进入豪华套房，获得一层护盾");
            }
        }
    }


    /// <summary>
    /// 当玩家离开碰撞区域时调用
    /// </summary>
    /// <param name="other">碰撞体名称，确保对方Collider设置为 Trigger</param>
    private void OnTriggerExit2D(Collider2D other)
    {
        if (other.CompareTag("BasementTrigger"))
        {
            inBasement = false;
            Debug.Log("玩家离开地下室");
        }
    }
    
    /// <summary>
    /// 玩家进入房间时调用，根据房间类型处理地下室和护盾逻辑
    /// </summary>
    /// <param name="roomType"></param>
    void OnEnterRoom(RoomType roomType)
    {
        try
        {
            if (roomType == RoomType.Basement && !inBasement)
            {
                inBasement = true;
                Debug.Log($"OnEnterRoom: Basement. hasShield={hasShield}, inBasement={inBasement}");
            }
            else if (roomType != RoomType.Basement && inBasement)
            {
                inBasement = false;
                Debug.Log("OnEnterRoom: left Basement or entered non-basement room");
                
            }

            // 豪华房进入一次授予一层护盾（不叠加）
            if (roomType == RoomType.Luxury && !hasShield)
            {
                hasShield = true;
                Debug.Log("OnEnterRoom: entered Luxury room, granted one shield (non-stacking)");
            }
        }
        catch (Exception e)
        {
            Debug.LogWarning($"PlayerController.OnEnterRoom exception: {e.Message}");
        }
    }

    void OnDestroy()
    {
        try
        {
            GameEventSystem.RemoveListener<RoomType>(GameEventType.EnterRoom, OnEnterRoom);
        }
        catch (Exception) { }
    }


     public void PlayerObtainKey()
    {
        hasKey = true;
    }

    public void PlayerUseKey()
    {
        hasKey = false;
    }


}