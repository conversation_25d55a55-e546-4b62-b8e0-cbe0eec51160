using UnityEngine;

public class SingletonMonoAuto<T> : MonoBehaviour where T : MonoBehaviour
{
    private static T m_instance;
    public static T Instance
    {
        get
        {
            if (m_instance == null)
            {
                GameObject obj = new()
                {
                    name = typeof(T).ToString()
                };
                m_instance = obj.AddComponent<T>();
                DontDestroyOnLoad(obj);
            }
            return m_instance;
        }
    }
}
