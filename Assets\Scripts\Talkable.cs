﻿using UnityEngine;

public class Talkable : MonoBehaviour
{
    [Header("交互提示内容")]
    [TextArea(1, 3)]
    public string[] lines;

    private bool isEntered;
    private bool wasHighlighted; // 记录当前是否已高亮

    [Header("贴图设置")]
    public Sprite normalSprite;      // 默认贴图
    public Sprite highlightSprite;   // 玩家靠近时的贴图

    private SpriteRenderer spriteRenderer;

    private void Awake()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();

        // 若未设置默认贴图，则自动读取当前贴图
        if (normalSprite == null && spriteRenderer != null)
            normalSprite = spriteRenderer.sprite;
    }

    private void OnTriggerEnter2D(Collider2D other)
    {
        if (other.CompareTag("Player"))
        {
            isEntered = true;
            UpdateHighlightState();
        }
    }

    private void OnTriggerExit2D(Collider2D other)
    {
        if (other.CompareTag("Player"))
        {
            isEntered = false;
            UpdateHighlightState();
        }
    }

    private void Update()
    {
        // 当玩家靠近且没有打开 Tips 时，按 E 开启对话
        if (isEntered && Input.GetKeyDown(KeyCode.E) && !TipsManager.Instance.isTips)
        {
            TipsManager.Instance.ShowTips(lines);
        }

        // 每帧检查高亮状态是否需要更新（Tips 打开时应自动取消高亮）
        UpdateHighlightState();
    }

    private void UpdateHighlightState()
    {
        // 判断是否应该高亮
        bool shouldHighlight = isEntered && !TipsManager.Instance.isTips;

        if (shouldHighlight != wasHighlighted) // 状态发生变化时才更新贴图
        {
            if (shouldHighlight && highlightSprite != null)
                spriteRenderer.sprite = highlightSprite;
            else
                spriteRenderer.sprite = normalSprite;

            wasHighlighted = shouldHighlight;
        }
    }
}
