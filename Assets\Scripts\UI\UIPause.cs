﻿using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class UIPause : UIPanel
{
    public Button resumeButton;
    public Button quitButton;
    public Slider musicSlider;
    public Slider sfxSlider;

    protected override void Awake()
    {
        base.Awake();

        // 按钮事件
        resumeButton.onClick.AddListener(() => UIManager.Instance.HideUI<UIPause>());
        quitButton.onClick.AddListener(() => SceneManager.LoadScene("MainMenu"));

        // 初始化滑条
        musicSlider.value = AudioManager.Instance.GetMusicVolume();
        sfxSlider.value = AudioManager.Instance.GetSFXVolume();

        // 滑条事件
        musicSlider.onValueChanged.AddListener(AudioManager.Instance.SetMusicVolume);
        sfxSlider.onValueChanged.AddListener(AudioManager.Instance.SetSFXVolume);
    }

    public override void OnShow(object param = null)
    {
        base.OnShow(param);
        Time.timeScale = 0f; // 暂停游戏
    }

    public override void OnHide()
    {
        base.OnHide();
        Time.timeScale = 1f; // 恢复游戏

    }
}
