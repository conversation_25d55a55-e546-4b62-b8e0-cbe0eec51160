using UnityEngine;

/// <summary>
/// 游戏全局音频管理器
/// 功能：播放音乐、音效，控制音量，保存设置
/// </summary>
public class AudioManager : SingletonMono<AudioManager>
{
    [Header("音频源")]
    public AudioSource musicSource; // 背景音乐
    public AudioSource sfxSource;   // 音效

    [Header("默认音量")]
    [Range(0f, 1f)] public float defaultMusicVolume = 0.8f;
    [Range(0f, 1f)] public float defaultSFXVolume = 0.8f;

    private const string MUSIC_VOLUME_KEY = "MusicVolume";
    private const string SFX_VOLUME_KEY = "SFXVolume";

    protected override void Awake()
    {
        base.Awake();

        // 初始化两个音频源
        if (musicSource == null)
        {
            musicSource = gameObject.AddComponent<AudioSource>();
            musicSource.loop = true;
        }

        if (sfxSource == null)
        {
            sfxSource = gameObject.AddComponent<AudioSource>();
            sfxSource.loop = false;
        }

        // 加载上次保存的音量
        LoadVolumeSettings();
    }

    /// <summary>
    /// 播放背景音乐
    /// </summary>
    public void PlayMusic(AudioClip clip, bool loop = true)
    {
        if (clip == null)
        {
            Debug.LogWarning("播放音乐失败：AudioClip为空。");
            return;
        }

        musicSource.clip = clip;
        musicSource.loop = loop;
        musicSource.Play();
    }

    /// <summary>
    /// 停止背景音乐
    /// </summary>
    public void StopMusic()
    {
        musicSource.Stop();
    }

    /// <summary>
    /// 播放音效
    /// </summary>
    public void PlaySFX(AudioClip clip)
    {
        if (clip == null)
        {
            Debug.LogWarning("播放音效失败：AudioClip为空。");
            return;
        }

        sfxSource.PlayOneShot(clip);
    }

    /// <summary>
    /// 根据音效名称播放（需放在 Resources/Audio 下）
    /// </summary>
    public void PlaySFX(string sfxName)
    {
        AudioClip clip = Resources.Load<AudioClip>($"Audio/{sfxName}");
        if (clip != null)
            PlaySFX(clip);
        else
            Debug.LogWarning($"未找到音效：Audio/{sfxName}");
    }

    //======================= 音量控制 =======================//

    public void SetMusicVolume(float volume)
    {
        musicSource.volume = volume;
        PlayerPrefs.SetFloat(MUSIC_VOLUME_KEY, volume);
    }

    public void SetSFXVolume(float volume)
    {
        sfxSource.volume = volume;
        PlayerPrefs.SetFloat(SFX_VOLUME_KEY, volume);
    }

    public float GetMusicVolume() => musicSource.volume;
    public float GetSFXVolume() => sfxSource.volume;

    void LoadVolumeSettings()
    {
        float musicVol = PlayerPrefs.GetFloat(MUSIC_VOLUME_KEY, defaultMusicVolume);
        float sfxVol = PlayerPrefs.GetFloat(SFX_VOLUME_KEY, defaultSFXVolume);

        musicSource.volume = musicVol;
        sfxSource.volume = sfxVol;
    }
}
