﻿using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;
using System.IO;

public class Portal : MonoBehaviour
{
    [Header("目标设置")]
    public string targetSceneAddress;     // Addressables 场景路径
    public string targetSpawnPointName;   // 目标出生点名称
    public RoomDirection roomDirection;   // 房间传送方向
    public bool isDifferentScene = false; // 是否跨场景传送

    [Header("状态设置")]
    public bool isActive = false;        // 传送门是否激活
    public bool isLocked = false;         // 是否上锁
    public bool requiresKey = false;      // 是否需要钥匙解锁
    private bool isTeleporting = false;   // 协程是否正在执行
    private bool playerInRange = false;   // 玩家是否在传送门范围内
    
    [Header("音效与特效")]
    public AudioSource unlockSound;       //尝试解锁成功音效
    public AudioSource lockedSound;       //尝试解锁失败音效
    public AudioSource teleportSound;

    private GameObject player;            // 玩家引用

    SpriteRenderer spriteRenderer;

    void Awake()
    {
        GameManager.Instance.RegisterTeleportPoint(gameObject.name, gameObject);
        GameEventSystem.AddListener(GameEventType.TransitionComplete, OnTransitionComplete);

        spriteRenderer = GetComponent<SpriteRenderer>();
    }

    private void OnTransitionComplete()
    {
        isActive = true;
    }

    private void OnTriggerEnter2D(Collider2D other)
    {
        if (other.CompareTag("Player"))
        {
            playerInRange = true;
            player = other.gameObject;
            spriteRenderer.enabled = true;
        }
    }

    private void OnTriggerExit2D(Collider2D other)
    {
        if (other.CompareTag("Player"))
        {
            playerInRange = false;
            player = null;
            spriteRenderer.enabled = false;
        }
    }

    private void Update()
    {
        // 检测方向键或 WASD 输入，修改房间传送方向
        if (isActive && playerInRange && !isTeleporting)
        {
            if (Input.GetKeyDown(KeyCode.UpArrow) || Input.GetKeyDown(KeyCode.W))
            {
                roomDirection = RoomDirection.Up;
            }
            else if (Input.GetKeyDown(KeyCode.DownArrow) || Input.GetKeyDown(KeyCode.S))
            {
                roomDirection = RoomDirection.Down;
            }
            else if (Input.GetKeyDown(KeyCode.LeftArrow) || Input.GetKeyDown(KeyCode.A))
            {
                roomDirection = RoomDirection.Left;
            }
            else if (Input.GetKeyDown(KeyCode.RightArrow) || Input.GetKeyDown(KeyCode.D))
            {
                roomDirection = RoomDirection.Right;
            }
        }

        if (isActive && playerInRange && !isTeleporting && Input.GetKeyDown(KeyCode.E))
        {
            TryActivatePortal();
        }
    }

     private void TryActivatePortal()
    {
        if (isLocked)
        {
            PlayerController playerController = player.GetComponent<PlayerController>();

            if (requiresKey && playerController != null && playerController.hasKey)
            {
                // 使用钥匙解锁
                playerController.PlayerUseKey();
                isLocked = false;
                if (unlockSound) unlockSound.Play();

                StartCoroutine(TeleportPlayer(player));
            }
            else
            {
                if (lockedSound) lockedSound.Play();
            }
        }
        else
        {
            StartCoroutine(TeleportPlayer(player));
        }
    }


    private IEnumerator TeleportPlayer(GameObject player)
    {
        if (player == null || TransitionManager.Instance.IsFading)
            yield break;

        isTeleporting = true;
        if (teleportSound) teleportSound.Play();

        PlayerController playerController = player.GetComponent<PlayerController>();
        if (playerController != null)
        {
            playerController.isControllable = false;
            playerController.isInvincible = true;
            Rigidbody2D rb = player.GetComponent<Rigidbody2D>();
            if (rb != null) rb.velocity = Vector2.zero; // 防止惯性滑动
        }

        string currentSceneName = SceneManager.GetActiveScene().name;
        string targetSceneName = Path.GetFileNameWithoutExtension(targetSceneAddress);

        // 如果目标场景就是当前场景，只传送玩家
        if (!isDifferentScene)
        {
            Room nextRoom = RoomManager.Instance.GetNextRoom(roomDirection);
            if (nextRoom)
            {
                yield return StartCoroutine(TransitionManager.Instance.Fade(1f));
                player.transform.position = nextRoom.spawnPoint.transform.position;
                RoomManager.Instance.CurrentRoom = nextRoom;
                GameEventSystem.Trigger(GameEventType.EnterRoom, nextRoom.roomType);
                yield return StartCoroutine(TransitionManager.Instance.Fade(0f));
            }
            else
            {
                Debug.LogWarning("Portal: 找不到目标房间 方向: " + roomDirection);
            }
        }
        else
        {
            // 跨场景传送
            GameManager.Instance.TargetSpawnPointName = targetSpawnPointName;
            TransitionManager.Instance.TransitionTo(targetSceneAddress);
            yield break;
        }

        if (playerController != null)
        {
            playerController.isControllable = true;
            playerController.isInvincible = false;
        }

        isTeleporting = false;
    }

    void OnDestroy()
    {
        GameManager.Instance.UnregisterTeleportPoint(gameObject.name);
        GameEventSystem.RemoveListener(GameEventType.TransitionComplete, OnTransitionComplete);
    }
}
