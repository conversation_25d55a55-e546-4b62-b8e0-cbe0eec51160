using System;
using System.Collections.Generic;

// 无参数事件处理委托
public delegate void EventDelegate();

// 单参数事件处理委托
public delegate void EventDelegate<T>(T param);

// 双参数的事件处理委托
public delegate void EventDelegate<T1, T2>(T1 param1, T2 param2);

public static class GameEventSystem
{
    // 统一使用 Delegate 基类，通过类型检查确保类型安全
    private static readonly Dictionary<int, Delegate> eventTable = new();

    #region 事件监听器管理

    /// <summary>
    /// 订阅无参数事件
    /// </summary>
    public static void AddListener(GameEventType gameEventType, EventDelegate handler)
    {
        AddListener<EventDelegate>(gameEventType, handler);
    }

    /// <summary>
    /// 订阅单参数事件
    /// </summary>
    public static void AddListener<T>(GameEventType gameEventType, EventDelegate<T> handler)
    {
        AddListener<EventDelegate<T>>(gameEventType, handler);
    }

    /// <summary>
    /// 订阅双参数事件
    /// </summary>
    public static void AddListener<T1, T2>(GameEventType gameEventType, EventDelegate<T1, T2> handler)
    {
        AddListener<EventDelegate<T1, T2>>(gameEventType, handler);
    }

    /// <summary>
    /// 取消订阅无参数事件
    /// </summary>
    public static void RemoveListener(GameEventType gameEventType, EventDelegate handler)
    {
        RemoveListener<EventDelegate>(gameEventType, handler);
    }

    /// <summary>
    /// 取消订阅单参数事件
    /// </summary>
    public static void RemoveListener<T>(GameEventType gameEventType, EventDelegate<T> handler)
    {
        RemoveListener<EventDelegate<T>>(gameEventType, handler);
    }

    /// <summary>
    /// 取消订阅双参数事件
    /// </summary>
    public static void RemoveListener<T1, T2>(GameEventType gameEventType, EventDelegate<T1, T2> handler)
    {
        RemoveListener<EventDelegate<T1, T2>>(gameEventType, handler);
    }

    #endregion

    #region 事件触发

    /// <summary>
    /// 触发无参数事件
    /// </summary>
    public static void Trigger(GameEventType gameEventType)
    {
        int eventType = (int)gameEventType;
        if (eventTable.TryGetValue(eventType, out var callback) && callback is EventDelegate typedCallback)
        {
            typedCallback.Invoke();
        }
    }

    /// <summary>
    /// 触发单参数事件
    /// </summary>
    public static void Trigger<T>(GameEventType gameEventType, T param)
    {
        int eventType = (int)gameEventType;
        if (eventTable.TryGetValue(eventType, out var callback) && callback is EventDelegate<T> typedCallback)
        {
            typedCallback.Invoke(param);
        }
    }

    /// <summary>
    /// 触发双参数事件
    /// </summary>
    public static void Trigger<T1, T2>(GameEventType gameEventType, T1 param1, T2 param2)
    {
        int eventType = (int)gameEventType;
        if (eventTable.TryGetValue(eventType, out var callback) && callback is EventDelegate<T1, T2> typedCallback)
        {
            typedCallback.Invoke(param1, param2);
        }
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 通用的添加监听器方法
    /// </summary>
    private static void AddListener<TDelegate>(GameEventType gameEventType, TDelegate handler)
        where TDelegate : Delegate
    {
        if (handler == null) return;

        int eventType = (int)gameEventType;
        eventTable.TryGetValue(eventType, out var existingHandler);
        eventTable[eventType] = Delegate.Combine(existingHandler, handler);
    }

    /// <summary>
    /// 通用的移除监听器方法
    /// </summary>
    private static void RemoveListener<TDelegate>(GameEventType gameEventType, TDelegate handler)
        where TDelegate : Delegate
    {
        if (handler == null) return;

        int eventType = (int)gameEventType;
        if (eventTable.TryGetValue(eventType, out var existingHandler))
        {
            var newHandler = Delegate.Remove(existingHandler, handler);
            if (newHandler == null)
                eventTable.Remove(eventType);
            else
                eventTable[eventType] = newHandler;
        }
    }

    /// <summary>
    /// 清除所有事件监听器
    /// </summary>
    public static void ClearAllListeners()
    {
        eventTable.Clear();
    }

    /// <summary>
    /// 清除指定事件类型的所有监听器
    /// </summary>
    public static void ClearListeners(GameEventType gameEventType)
    {
        int eventType = (int)gameEventType;
        eventTable.Remove(eventType);
    }

    #endregion
}