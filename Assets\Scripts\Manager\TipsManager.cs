﻿using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class TipsManager : SingletonMono<TipsManager>
{
    public GameObject tipsBox;  //用以隐藏或显示整个文字提示窗口
    public TMP_Text tipsText;  //文字提示的内容
    public bool isTips;

    [TextArea(1, 3)]
    public string[] tipsLines;
    [SerializeField] private int currentLine;  //当前对话窗口正在进行哪一个元素的文字内容输出


    private void Start()
    {
        if(currentLine < tipsLines.Length)
        {
            tipsText.text = tipsLines[currentLine];
        }

    }

    private void Update()
    {
        TipsOn();
    }

    public void ShowTips(string[] newLines)
    {
        tipsLines = newLines;
        currentLine = 0;
        tipsText.text = tipsLines[currentLine];
        tipsBox.SetActive(true);

    }

    public void TipsOn()
    {
        isTips = tipsBox.activeInHierarchy;

        if (isTips)
        {
            if(currentLine == tipsLines.Length)
            {
                tipsBox.SetActive(false);
                return;
            }

            if (Input.GetMouseButtonUp(0)|| Input.GetKeyDown(KeyCode.Space))
            {
                currentLine++;

                if (currentLine < tipsLines.Length)
                {
                    tipsText.text = tipsLines[currentLine];
                }
                else
                {
                    tipsBox.SetActive(false);  //当前文字提示内容结束或为0时，使文本框不可见
                    
                }
            }
        }
    }
}
