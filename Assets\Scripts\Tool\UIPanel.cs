﻿using UnityEngine;
using UnityEngine.UI;

public abstract class UIPanel : MonoBehaviour
{
    public bool hasMaskBackground = true;
    public float maskBackgroundAlpha = 0.5f;
    public CanvasDefine canvasDefine = CanvasDefine.Normal;

    protected Canvas canvas;
    protected GraphicRaycaster graphicRaycaster;
    protected CanvasGroup canvasGroup;

    public Canvas Canvas => canvas;
    public GraphicRaycaster GraphicRaycaster => graphicRaycaster;
    public CanvasGroup CanvasGroup => canvasGroup;

    protected virtual void Awake()
    {
        InitializeComponents();

        if (hasMaskBackground)
        {
            CreateMaskBackground();
        }

        OnInit();
    }

    void InitializeComponents()
    {
        canvas = GetComponent<Canvas>();
        if (canvas == null)
        {
            canvas = gameObject.AddComponent<Canvas>();
        }

        graphicRaycaster = GetComponent<GraphicRaycaster>();
        if (graphicRaycaster == null)
        {
            graphicRaycaster = gameObject.AddComponent<GraphicRaycaster>();
        }

        canvasGroup = GetComponent<CanvasGroup>();
        if (canvasGroup == null)
        {
            canvasGroup = gameObject.AddComponent<CanvasGroup>();
        }
    }

    protected void CreateMaskBackground()
    {
        // 创建遮罩背景
        GameObject maskBackground = new("MaskBackground");
        maskBackground.transform.SetParent(transform, false);
        maskBackground.transform.SetAsFirstSibling();

        // 添加必要的组件
        var rectTrans = maskBackground.AddComponent<RectTransform>();
        rectTrans.anchorMin = Vector2.zero;
        rectTrans.anchorMax = Vector2.one;
        rectTrans.sizeDelta = Vector2.zero;
        rectTrans.localScale = Vector3.one;

        var image = maskBackground.AddComponent<Image>();
        image.color = new Color(0, 0, 0, maskBackgroundAlpha);

        // 添加点击事件
        var button = maskBackground.AddComponent<Button>();
        button.onClick.AddListener(Hide);

        maskBackground.SetActive(true);
    }

    public virtual void OnInit()
    {
        
    }

    public virtual void OnShow(object param = null)
    {
        if (canvasGroup != null)
        {
            canvasGroup.alpha = 1f;
            canvasGroup.interactable = true;
            canvasGroup.blocksRaycasts = true;
        }

        GameEventSystem.Trigger(GameEventType.UIOpened, GetType().Name);
    }

    public virtual void OnHide()
    {
        GameEventSystem.Trigger(GameEventType.UIClosed, GetType().Name);
    }

    public virtual void OnResume()
    {
        if (canvasGroup != null)
        {
            canvasGroup.alpha = 1f;
            canvasGroup.interactable = true;
            canvasGroup.blocksRaycasts = true;
        }
    }

    public virtual void OnPause()
    {
        if (canvasGroup != null)
        {
            canvasGroup.alpha = 0f;
            canvasGroup.interactable = false;
            canvasGroup.blocksRaycasts = false;
        }
    }

    public void SetSortingOrder(int order)
    {
        if (canvas != null)
        {
            canvas.sortingOrder = order;
        }
    }

    void Hide()
    {
        UIManager.Instance.HideTopUI();
    }
}
