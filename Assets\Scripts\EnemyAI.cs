    using UnityEngine;

    public class EnemyAI : MonoBehaviour
    {
        [Header("基本设置")]
        public float moveSpeed = 2f;           // 巡逻速度
        public float chaseSpeed = 3.5f;        // 追击速度
        public float visionRange = 5f;         // 视野距离
        public float returnDelay = 2f;         // 离开视野后返回的等待时间
        public float stopDistance = 0.1f;      // 距离玩家多近时停止

        [Header("检测")]
        public Transform groundCheck;          // 检测地面的空物体（放在脚底）
        public Transform wallCheck;            // 检测前方墙壁的空物体（放在身体前方）
        public LayerMask groundLayer;          // Ground层

        public float flipCooldown = 0.1f;      // 翻转cd

        private Rigidbody2D rb;
        private Transform player;
        private Vector2 startPos;              // 敌人初始位置，用于返回
        private bool isChasing = false;        // 是否处于追击状态
        private bool isReturning = false;      // 是否处于返回原点状态 
        private bool facingRight = true;       // 敌人当前面向方向 
        private float lastFlipTime;            // 上一次翻转时间，用于冷却

        
        void Start()
        {
            rb = GetComponent<Rigidbody2D>();
            player = GameObject.FindGameObjectWithTag("Player").transform;
            startPos = transform.position; // 敌人初始位置，可用于重生
        }

        void Update()
        {
            float distanceToPlayer = Mathf.Abs(player.position.x - transform.position.x);

            if (!isChasing && distanceToPlayer < visionRange)
            {
                isChasing = true;
                isReturning = false;
            }
            else if (isChasing && distanceToPlayer > visionRange)
            {
                // Player 离开视野后等待一段时间再返回
                isChasing = false;
                Invoke(nameof(StartReturn), returnDelay);
            }

            FlipTowardsTarget(); // 根据玩家位置翻转敌人朝向（追击状态下）
        }

        void FixedUpdate()
        {
            // 检测脚下是否有地面
            bool isGrounded = Physics2D.OverlapCircle(groundCheck.position, 0.1f, groundLayer);
            // 检测前方是否有墙壁
            bool hitWall = Physics2D.OverlapCircle(wallCheck.position, 0.1f, groundLayer);

            if (!isGrounded)
                return; 

            
            if (isChasing)
            {
                ChasePlayer();
            }
            else if (isReturning)
            {
                ReturnToStart(); // 返回初始位置
            }
            else
            {
                Patrol(hitWall);
            }
        }

        void ChasePlayer()
        {
            float xDiff = player.position.x - transform.position.x;

            // 横向差小于阈值时不翻转
            if (Mathf.Abs(xDiff) >= 0.1f)
            {
                bool shouldFaceRight = xDiff > 0;
                if (shouldFaceRight != facingRight)
                    Flip();
            }

            float direction = Mathf.Sign(xDiff);
            
            if (Mathf.Abs(xDiff) < stopDistance)
            {
                rb.velocity = Vector2.zero;
                return;
            }
            rb.velocity = new Vector2(direction * chaseSpeed, rb.velocity.y);
        }

        /// <summary>
        /// 执行实际移动，把敌人移动回初始点
        /// </summary>
        void ReturnToStart()
        {
            float direction = Mathf.Sign(startPos.x - transform.position.x);
            rb.velocity = new Vector2(direction * moveSpeed, rb.velocity.y);

            if (Mathf.Abs(transform.position.x - startPos.x) < 0.1f)
            {
                rb.velocity = Vector2.zero;
                isReturning = false;
            }
        }

        void Patrol(bool hitWall)
        {
            if (isChasing || isReturning) return;
            
            // 没有目标时左右巡逻
            rb.velocity = new Vector2((facingRight ? 1 : -1) * moveSpeed, rb.velocity.y);

            // 前方无地面或撞墙 -> 转向
            bool hasGroundAhead = Physics2D.OverlapCircle(groundCheck.position + (facingRight ? Vector3.right : Vector3.left) * 0.5f, 0.1f, groundLayer);
            
            if (!hasGroundAhead || hitWall)
            {
                Flip();
            }
        }

        void StartReturn()
        {
            isReturning = true;
        }

        /// <summary>
        /// 判断何时执行翻转动作
        /// </summary>
        void FlipTowardsTarget()
        {
            
            if (isChasing)
            {
                float xDiff = player.position.x - transform.position.x;

                // 横向差小于阈值时不翻转
                if (Mathf.Abs(xDiff) < 0.1f) 
                    return;

                bool shouldFaceRight = xDiff > 0;
                if (shouldFaceRight != facingRight)
                    Flip();
            }
        }

        /// <summary>
        /// 执行翻转动作的主函数
        /// </summary>
        void Flip()
        {     
            if (Time.time - lastFlipTime < flipCooldown) return;
            facingRight = !facingRight;
            transform.localScale = new Vector3(-transform.localScale.x, transform.localScale.y, transform.localScale.z);
            lastFlipTime = Time.time;
        }

        void OnDrawGizmosSelected()
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, visionRange);

            if (groundCheck != null)
                Gizmos.DrawWireSphere(groundCheck.position, 0.1f);
        }
        
        public void EnemyResetPosition()    
        {
            transform.position = startPos;
            rb.velocity = Vector2.zero;

            // 重置状态
            isChasing = false;
            isReturning = false;
        }

        void OnEnable()
        {
            GameEventSystem.AddListener(GameEventType.PlayerRespawned, OnPlayerRespawned);
            GameEventSystem.AddListener(GameEventType.SceneLoaded, OnSceneLoaded);
        }

        void OnDisable()
        {
            GameEventSystem.RemoveListener(GameEventType.PlayerRespawned, OnPlayerRespawned);
            GameEventSystem.RemoveListener(GameEventType.SceneLoaded, OnSceneLoaded);
        }

        private void OnPlayerRespawned()
        {
            EnemyResetPosition();
        }

        private void OnSceneLoaded()
        {
            EnemyResetPosition();
        }

    }
