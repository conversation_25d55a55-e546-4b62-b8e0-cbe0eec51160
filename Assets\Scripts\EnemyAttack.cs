using UnityEngine;

public class EnemyAttack : MonoBehaviour
{
    public int damage = 3;                 // 每次碰到玩家造成的伤害
    public float damageCooldown = 1f;      // 冷却时间，防止血量瞬间掉光

    private float lastDamageTime = -10f;

    void OnCollisionEnter2D(Collision2D collision)
    {
        TryDealDamage(collision.collider);
    }

    void OnTriggerEnter2D(Collider2D other)
    {
        TryDealDamage(other);
    }

    private void TryDealDamage(Collider2D col)
    {
        if (col.CompareTag("Player"))
        {
            if (Time.time - lastDamageTime < damageCooldown) return;

            PlayerController player = col.GetComponent<PlayerController>();
            if (player != null)
            {
                player.TakeDamage(damage);
                lastDamageTime = Time.time;
            }
        }
    }
}
