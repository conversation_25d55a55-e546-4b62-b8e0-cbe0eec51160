﻿using UnityEngine;
using System.Collections;
using UnityEngine.SceneManagement;
using System;

public class TransitionManager : SingletonMono<TransitionManager>
{
    [Header("Fade 设置")]
    public CanvasGroup fadeCanvasGroup;
    public float fadeDuration = 1f;
    private bool isFading = false;
    public bool IsFading => isFading;

    [Header("第一关地址")]
    public string firstScenePath;

    private bool pendingReturnToFirstScene = false;
    private Transform firstSceneStartPoint;  // 第一关的重生点

/*

    public void SetFirstSceneStartPoint(Transform startPoint)
    {
        firstSceneStartPoint = startPoint;
    }

    public Transform GetFirstSceneStartPoint()
    {
        return firstSceneStartPoint;
    }
*/

    /// <summary>
    /// 以淡入淡出形式切换场景
    /// </summary>
    /// <param name="toScenePath">目标场景的 Addressable 路径</param>
    public void TransitionTo(string toScenePath)
    {
        if (!isFading)
            StartCoroutine(TransitionCoroutine(toScenePath));
    }

    public IEnumerator StartFade(float targetAlpha) 
    {
        return Fade(targetAlpha); 
    }

    private IEnumerator TransitionCoroutine(string toScenePath)
    {
        isFading = true;
        fadeCanvasGroup.blocksRaycasts = true;

        //淡出
        yield return StartCoroutine(Fade(1f));

        //卸载当前场景
        bool unloadDone = false;
        ResourceManager.Instance.UnloadCurrentLevelScene(() =>
        {
            unloadDone = true;
        });
        yield return new WaitUntil(() => unloadDone);

        //加载新场景
        bool loadDone = false;
        Scene loadedScene = default;
        ResourceManager.Instance.LoadSceneAdditive(toScenePath, (scene) =>
        {
            loadedScene = scene;
            loadDone = true;
        });
        yield return new WaitUntil(() => loadDone);

        //设置新场景为激活场景
        if (loadedScene.IsValid())
        {
            SceneManager.SetActiveScene(loadedScene);
        }

        GameEventSystem.Trigger(GameEventType.SceneLoaded);

        //只在本次转场为“回到第一关”时把玩家放到第一关起点并复活
        if (pendingReturnToFirstScene)
        {
            //如果第一关起点已设置，则把玩家放过去并复活
            if (firstSceneStartPoint != null)
            {
                PlayerController player = FindObjectOfType<PlayerController>();
                if (player != null)
                {
                    player.transform.position = firstSceneStartPoint.position;
                    player.Revive();
                }
            }
            else
            {
                Debug.LogWarning("pendingReturnToFirstScene 为 true，但 firstSceneStartPoint 未设置。");
            }

            // 重置标志
            pendingReturnToFirstScene = false;
        }

        //淡入
        yield return StartCoroutine(Fade(0f));

        fadeCanvasGroup.blocksRaycasts = false;
        isFading = false;

        // 恢复玩家可控状态（保险措施）：跨场景传送或多次交互可能导致某些路径未能正确恢复玩家控制，
        // 在转场完成时显式确保玩家可操作并取消无敌状态。
        try
        {
            var player = FindObjectOfType<PlayerController>();
            if (player != null)
            {
                player.isControllable = true;
                player.isInvincible = false;
            }
        }
        catch (Exception) { }

        GameEventSystem.Trigger(GameEventType.TransitionComplete);
    }

    /// <summary>
    /// 控制画面淡入淡出（alpha：1为黑，0为透明）
    /// </summary>
    public IEnumerator Fade(float targetAlpha)
    {
        isFading = true;
        float startAlpha = fadeCanvasGroup.alpha;
        float elapsed = 0f;

        while (!Mathf.Approximately(fadeCanvasGroup.alpha, targetAlpha))
        {
            elapsed += Time.deltaTime;
            fadeCanvasGroup.alpha = Mathf.MoveTowards(startAlpha, targetAlpha, elapsed / fadeDuration);
            yield return null;
        }
        isFading = false;
    }

    public void TransitionToCurrentRoom(Transform respawnPos)
    {
        if (!isFading)
            StartCoroutine(RespawnInCurrentRoom(respawnPos));
    }

    private IEnumerator RespawnInCurrentRoom(Transform respawnPos)
    {
        isFading = true;
        fadeCanvasGroup.blocksRaycasts = true;

        yield return StartCoroutine(Fade(1f));  // 淡出

        // 找到玩家
        PlayerController player = FindObjectOfType<PlayerController>();

        // 重置敌人位置
        EnemyAI enemy = FindObjectOfType<EnemyAI>();
        if (enemy != null)
        {
            enemy.EnemyResetPosition();
        }

        if (player != null)
        {
            player.Revive();
            // 如果需要把玩家放到 respawnPos（当前场景的重生点）
            if (respawnPos != null)
                player.transform.position = respawnPos.position;
        }

        yield return StartCoroutine(Fade(0f));  // 淡入

        fadeCanvasGroup.blocksRaycasts = false;
        isFading = false;
    }

    /// <summary>
    /// 设置第一关的重生点（由 GameManager 在加载第一关时调用）
    /// </summary>
    public void SetFirstSceneStartPoint(Transform startPoint)   
    {
        firstSceneStartPoint = startPoint;
    }

    /// <summary>
    /// 获取第一关重生点（供 TransitionToFirstScene 使用）
    /// </summary>
    public Transform GetFirstSceneStartPoint()
    {
        return firstSceneStartPoint;
    }


    
    /// <summary>
    /// 专门用于回到第一关的转换入口
    /// </summary>
    public void TransitionToFirstScene()
    {
        if (string.IsNullOrEmpty(firstScenePath))
        {
            Debug.LogWarning("TransitionToFirstScene: firstScenePath 未设置");
            return;
        }

        if (!isFading && !string.IsNullOrEmpty(firstScenePath))
        {
            // 标记本次转场是回第一关
            pendingReturnToFirstScene = true;
            StartCoroutine(TransitionCoroutine(firstScenePath));
        }
    }


}
