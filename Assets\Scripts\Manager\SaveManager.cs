using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.SceneManagement;

public class SaveManager : SingletonMono<SaveManager>
{
    private const string SavePath = "Saves/";
    private const string SaveFileExtension = ".sav";
    private const int MaxSaveSlots = 3;
    // 后端并发保护：防止重复触发保存/加载
    private readonly HashSet<int> savingSlots = new HashSet<int>();
    private bool isLoading = false;
    
    // 保存请求队列：将快速的多次保存序列化处理，避免并发文件写入导致卡住
    class SaveRequest { public int Slot; public Action Callback; public SaveRequest(int s, Action c) { Slot = s; Callback = c; } }
    private readonly Queue<SaveRequest> saveQueue = new Queue<SaveRequest>();
    private bool isProcessingSaveQueue = false;
    private int maxSaveQueueSize = 6; // 最大排队数，超过则忽略多余请求
    private float saveTimeoutSeconds = 8f; // 单次保存（截图）超时，超时后强制清理并继续队列

    public void SaveGame(int slot, Action onComplete = null)
    {
        // 如果已经在队列或正在保存该槽位，忽略重复请求
        if (savingSlots.Contains(slot) || IsSlotInQueue(slot))
        {
            Debug.LogWarning($"Save already in progress or queued for slot {slot}");
            onComplete?.Invoke();
            return;
        }

        // 队列已满则拒绝，避免无限堆积
        if (saveQueue.Count >= maxSaveQueueSize)
        {
            Debug.LogWarning($"Save queue full, rejecting save for slot {slot}");
            onComplete?.Invoke();
            return;
        }

        saveQueue.Enqueue(new SaveRequest(slot, onComplete));
        if (!isProcessingSaveQueue)
        {
            StartCoroutine(ProcessSaveQueue());
        }
        Debug.Log($"Save request enqueued for slot {slot}. Queue size: {saveQueue.Count}");
    }

    bool IsSlotInQueue(int slot)
    {
        foreach (var req in saveQueue)
        {
            if (req.Slot == slot) return true;
        }
        return false;
    }

    private IEnumerator ProcessSaveQueue()
    {
        isProcessingSaveQueue = true;
        while (saveQueue.Count > 0)
        {
            var req = saveQueue.Dequeue();
            // 执行单次保存（会等截图完成）
            yield return StartCoroutine(PerformSaveCoroutine(req.Slot, req.Callback));
        }
        isProcessingSaveQueue = false;
    }

    private IEnumerator PerformSaveCoroutine(int slot, Action onComplete = null)
    {
        // 标记该槽正在保存
        try
        {
            savingSlots.Add(slot);
        }
        catch { }

        SaveData saveData = CollectSaveData();
        string json = JsonUtility.ToJson(saveData, true);
        string path = GetSaveFilePath(slot);

        Directory.CreateDirectory(Application.persistentDataPath + "/" + SavePath);
        try
        {
            File.WriteAllText(path, json);
        }
        catch (Exception e)
        {
            Debug.LogError($"SaveManager: failed to write save file for slot {slot}: {e.Message}");
            // 移除保存锁并触发回调后返回
            try { if (savingSlots.Contains(slot)) savingSlots.Remove(slot); } catch { }
            onComplete?.Invoke();
            yield break;
        }

        // 调用现有截图流程（带超时保护），避免截图协程卡住导致队列阻塞。
        bool callbackInvoked = false;
        bool captureDone = false;

        Action wrappedCallback = () =>
        {
            if (callbackInvoked) return;
            callbackInvoked = true;
            captureDone = true;
            try { onComplete?.Invoke(); } catch { }
        };

        Coroutine captureCoroutine = StartCoroutine(CaptureScreenshot(path, slot, wrappedCallback));

        float startTime = Time.realtimeSinceStartup;
        while (!captureDone && Time.realtimeSinceStartup - startTime < saveTimeoutSeconds)
        {
            yield return null;
        }

        if (!captureDone)
        {
            Debug.LogWarning($"SaveManager: CaptureScreenshot timed out for slot {slot} after {saveTimeoutSeconds} seconds. Continuing queue.");
            // 标记回调已被调用以防止后续重复回调
            if (!callbackInvoked)
            {
                callbackInvoked = true;
                try { onComplete?.Invoke(); } catch { }
            }

            // 尝试移除保存锁
            try { if (savingSlots.Contains(slot)) savingSlots.Remove(slot); } catch { }

            // 不要强制 StopCoroutine(captureCoroutine) 因为那会阻止 CaptureScreenshot 的 finally 清理；让它自然完成但不阻塞队列。
        }
    }

    private IEnumerator CaptureScreenshot(string path, int slot, Action onComplete = null)
    {
        // Use a temporary camera rendering to a RenderTexture so UI (Overlay/Popup) isn't visible in the capture.
        Camera mainCam = Camera.main;
        if (mainCam == null) mainCam = FindObjectOfType<Camera>();

        RenderTexture rt = null;
        GameObject tempCamGO = null;
        Camera tempCam = null;
        Texture2D tex = null;

        try
        {
            if (mainCam != null)
            {
                int width = Screen.width;
                int height = Screen.height;
                rt = new RenderTexture(width, height, 24, RenderTextureFormat.ARGB32);
                rt.Create();

                // Create a temporary camera that copies main camera settings
                tempCamGO = new GameObject("TempScreenshotCamera");
                tempCamGO.hideFlags = HideFlags.HideAndDontSave;
                tempCam = tempCamGO.AddComponent<Camera>();

                tempCam.transform.position = mainCam.transform.position;
                tempCam.transform.rotation = mainCam.transform.rotation;
                tempCam.fieldOfView = mainCam.fieldOfView;
                tempCam.orthographic = mainCam.orthographic;
                tempCam.orthographicSize = mainCam.orthographicSize;
                tempCam.clearFlags = mainCam.clearFlags;
                tempCam.backgroundColor = mainCam.backgroundColor;

                // Build culling mask: start from main camera, then exclude layers used by UI canvases (ScreenSpaceCamera/WorldSpace)
                int mask = mainCam.cullingMask;
                var canvases = FindObjectsOfType<Canvas>();
                foreach (var c in canvases)
                {
                    try
                    {
                        if (c.renderMode != RenderMode.ScreenSpaceOverlay)
                        {
                            int layer = c.gameObject.layer;
                            mask &= ~(1 << layer);
                        }
                    }
                    catch { }
                }
                tempCam.cullingMask = mask;

                tempCam.targetTexture = rt;

                // Render and read back
                yield return new WaitForEndOfFrame();
                tempCam.Render();
                RenderTexture.active = rt;
                tex = new Texture2D(rt.width, rt.height, TextureFormat.RGB24, false);
                tex.ReadPixels(new Rect(0, 0, rt.width, rt.height), 0, 0);
                tex.Apply();

                // Write PNG
                try
                {
                    File.WriteAllBytes(path + ".png", tex.EncodeToPNG());
                    onComplete?.Invoke();
                }
                catch (Exception e)
                {
                    Debug.LogWarning($"SaveManager: failed to write screenshot png for slot {slot}: {e.Message}");
                }
            }
            else
            {
                // Fallback to ScreenCapture if no camera found
                yield return new WaitForEndOfFrame();
                Texture2D screenshot = ScreenCapture.CaptureScreenshotAsTexture();
                try
                {
                    File.WriteAllBytes(path + ".png", screenshot.EncodeToPNG());
                    onComplete?.Invoke();
                }
                catch (Exception e)
                {
                    Debug.LogWarning($"SaveManager: fallback screenshot write failed for slot {slot}: {e.Message}");
                }
                finally
                {
                    UnityEngine.Object.Destroy(screenshot);
                }
            }
        }
        finally
        {
            // cleanup
            try { if (tex != null) UnityEngine.Object.Destroy(tex); } catch { }
            try { RenderTexture.active = null; } catch { }
            try { if (tempCam != null) tempCam.targetTexture = null; } catch { }
            try { if (rt != null) { rt.Release(); UnityEngine.Object.Destroy(rt); } } catch { }
            try { if (tempCamGO != null) UnityEngine.Object.Destroy(tempCamGO); } catch { }

            // Ensure slot lock removed
            try { if (savingSlots.Contains(slot)) savingSlots.Remove(slot); } catch { }
        }
    }


    public void LoadGame(int slot, Action callback = null)
    {
        if (isLoading)
        {
            Debug.LogWarning($"Load already in progress. Ignoring request for slot {slot}.");
            callback?.Invoke();
            return;
        }
        isLoading = true;

        string path = GetSaveFilePath(slot);
        if (!File.Exists(path))
        {
            Debug.LogWarning($"槽位 {slot} 的存档文件不存在: {path}");
            isLoading = false;
            callback?.Invoke();
            return;
        }

        try
        {
            string json = File.ReadAllText(path);
            SaveData saveData = JsonUtility.FromJson<SaveData>(json);
            if (saveData == null)
            {
                Debug.LogError($"存档文件 {path} 格式错误");
                callback?.Invoke();
                return;
            }
            StartCoroutine(LoadGameCoroutine(saveData, callback));
        }
        catch (Exception e)
        {
            Debug.LogError($"加载存档失败: {e.Message}");
            callback?.Invoke();
        }
    }

    private SaveData CollectSaveData()
    {
        SaveData saveData = new SaveData
        {
            saveTime = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
        };
        PlayerController player = FindObjectOfType<PlayerController>();
        RoomManager roomManager = RoomManager.Instance;
        GameManager gameManager = GameManager.Instance;
        UIManager uiManager = UIManager.Instance;

        if (player != null)
        {
            saveData.playerPosition = player.transform.position;
            saveData.currentHealth = player.CurrentHealth;
            saveData.san = player.san;
            saveData.hasShield = player.hasShield;
            saveData.hasKey = player.hasKey;
            saveData.hasLetter = player.hasLetter;
            saveData.extraMove = player.ExtraMove;
            saveData.letterLost = player.LetterLost;
            saveData.inBasement = player.InBasement;
        }
        else
        {
            Debug.LogWarning("PlayerController not found in scene. Saving default player data.");
            saveData.playerPosition = Vector2.zero;
            saveData.currentHealth = 0;
            saveData.san = 100f;
            saveData.hasShield = false;
            saveData.hasKey = false;
            saveData.hasLetter = false;
            saveData.extraMove = false;
            saveData.letterLost = false;
            saveData.inBasement = false;
        }

        var resManager = ResourceManager.Instance;
        if (resManager != null && resManager.CurrentScene.Scene.IsValid())
        {
            saveData.currentScenePath = resManager.CurrentScene.Scene.path;
        }
        else
        {
            Debug.LogWarning("ResourceManager or currentScene is invalid. Saving empty scene path.");
            saveData.currentScenePath = "";
        }
        saveData.targetSpawnPointName = gameManager != null ? gameManager.TargetSpawnPointName : "";

        if (roomManager != null && roomManager.CurrentRoom != null)
        {
            saveData.currentRoomRow = roomManager.CurrentRoom.row;
            saveData.currentRoomCol = roomManager.CurrentRoom.col;
            // 保存当前房间的类型名称，便于在存档界面显示
            try
            {
                // 优先保存友好展示名（中文映射），若发生异常则回退为空字符串
                saveData.currentRoomTypeName = RoomTypeDisplay.GetDisplayName(roomManager.CurrentRoom.roomType);
            }
            catch (Exception)
            {
                saveData.currentRoomTypeName = "";
            }
            saveData.markDatas = new List<MarkData>(roomManager.MarkDatas ?? new List<MarkData>());
            saveData.systemMarkDatas = new List<MarkData>(roomManager.SystemMarkDatas ?? new List<MarkData>());
        }
        else
        {
            Debug.LogWarning("RoomManager or CurrentRoom is null. Saving default room data.");
            saveData.currentRoomRow = 0;
            saveData.currentRoomCol = 0;
            saveData.markDatas = new List<MarkData>();
            saveData.systemMarkDatas = new List<MarkData>();
        }

        saveData.teleportPoints = new Dictionary<string, Vector2>();
        if (gameManager != null)
        {
            var points = gameManager.GetTeleportPoints();
            if (points != null)
            {
                foreach (var point in points)
                {
                    if (point.Value != null)
                    {
                        saveData.teleportPoints[point.Key] = point.Value.transform.position;
                    }
                }
            }
        }
        return saveData;
    }
/*
        saveData.uiStack = new List<string>();
        if (uiManager != null)
        {
            var uiStack = uiManager.GetUIStack();
            if (uiStack != null)
            {
                foreach (var panel in uiStack)
                {
                    if (panel == null) continue;

                    // 不保存临时弹窗类（例如存档面板），避免加载时再次弹出保存/加载 UI
                    if (panel is UISavePanel) continue;

                    //saveData.uiStack.Add($"Assets/Prefabs/UI/{panel.GetType().Name}.prefab");
                }
            }
        }
*/
    private IEnumerator LoadGameCoroutine(SaveData saveData, Action callback)
    {
        isLoading = true;
        try
        {
        var transitionManager = TransitionManager.Instance;
        var player = FindObjectOfType<PlayerController>();
        var roomManager = RoomManager.Instance;
        var gameManager = GameManager.Instance;
        var uiManager = UIManager.Instance;
        
        if (uiManager != null)
        {
            uiManager.DisableUI();
        }
        
        if (player != null)
        {
            player.isControllable = false;
            player.isInvincible = true;
            Rigidbody2D rb = player.GetComponent<Rigidbody2D>();
            if (rb != null) rb.velocity = Vector2.zero; // 防止惯性滑动
        }


        yield return transitionManager?.StartFade(1f);

        bool unloadDone = false;
        ResourceManager.Instance.UnloadCurrentLevelScene(() => { unloadDone = true; });
        yield return new WaitUntil(() => unloadDone);

        bool loadDone = false;
        Scene loadedScene = default;
        ResourceManager.Instance.LoadSceneAdditive(saveData.currentScenePath, (scene) =>
        {
            if (!scene.IsValid())
            {
                Debug.LogError($"加载场景失败: {saveData.currentScenePath}");
                loadDone = true;
                return;
            }
            loadedScene = scene;
            loadDone = true;
        });
        yield return new WaitUntil(() => loadDone);

        if (loadedScene.IsValid())
        {
            SceneManager.SetActiveScene(loadedScene);
        }
        
        // 提前触发 SceneLoaded 事件，确保 RoomManager.RefreshRoomList 在设置房间之前完成
        GameEventSystem.Trigger(GameEventType.SceneLoaded);
        
        if (roomManager != null)
        {
            try
            {
                roomManager.RefreshRoomList();
                Debug.Log($"RoomList refreshed with {roomManager.RoomList?.Count} rooms");
            }
            catch (Exception e)
            {
                Debug.LogWarning($"SaveManager: Failed to refresh RoomManager list: {e.Message}");
            }
        }

        // 设置玩家位置和当前房间
        if (roomManager != null && roomManager.RoomList != null)
        {
            Debug.Log($"RoomList contains {roomManager.RoomList.Count} rooms");
            foreach (var room in roomManager.RoomList)
            {
                Debug.Log($"Room: index={room.index}, row={room.row}, col={room.col}, type={room.roomType}");
            }
            var savedRoom = roomManager.RoomList.Find(r => r.row == saveData.currentRoomRow && r.col == saveData.currentRoomCol);
            if (savedRoom != null)
            {
                roomManager.CurrentRoom = savedRoom;
                GameEventSystem.Trigger(GameEventType.EnterRoom, savedRoom.roomType);
                Debug.Log($"Set CurrentRoom to row={saveData.currentRoomRow}, col={saveData.currentRoomCol}, type={savedRoom.roomType}");
            }
            else
            {
                Debug.LogError($"SaveManager: saved room (row={saveData.currentRoomRow}, col={saveData.currentRoomCol}) not found in current scene. Scene path: {saveData.currentScenePath}");
            }

            roomManager.MarkDatas?.Clear();
            if (saveData.markDatas != null) roomManager.MarkDatas.AddRange(saveData.markDatas);
            roomManager.SystemMarkDatas?.Clear();
            if (saveData.systemMarkDatas != null) roomManager.SystemMarkDatas.AddRange(saveData.systemMarkDatas);
        }

        if (player != null)
        {
            player.transform.position = saveData.playerPosition;
            Debug.Log($"Player positioned at {saveData.playerPosition}, CurrentRoom: row={roomManager?.CurrentRoom?.row}, col={roomManager?.CurrentRoom?.col}");
            player.CurrentHealth = saveData.currentHealth;
            player.san = saveData.san;
            player.hasShield = saveData.hasShield;
            player.hasKey = saveData.hasKey;
            player.hasLetter = saveData.hasLetter;
            player.ExtraMove = saveData.extraMove;
            player.LetterLost = saveData.letterLost;
            player.InBasement = saveData.inBasement;
            player.Revive();
        }

       if (gameManager != null)
        {
            gameManager.ClearTeleportPoints();
            if (saveData.teleportPoints != null)
            {
                foreach (var point in saveData.teleportPoints)
                {
                    GameObject pointObj = new GameObject(point.Key);
                    pointObj.transform.position = point.Value;
                    gameManager.RegisterTeleportPoint(point.Key, pointObj);
                }
            }
            gameManager.TargetSpawnPointName = saveData.targetSpawnPointName;
        }
/*
        if (uiManager != null)
        {
            uiManager.ClosePanelsInCanvas(CanvasDefine.Popup);
            int loadedCount = 0;
            foreach (var uiPath in saveData.uiStack ?? new List<string>())
            {
                ResourceManager.Instance.InstantiatePrefab(uiPath, uiManager.transform, (go) =>
                {
                    if (go != null && go.TryGetComponent<UIPanel>(out var panel))
                    {
                        // 如果意外实例化了 UISavePanel，跳过显示
                        if (panel is UISavePanel)
                        {
                            Debug.LogWarning($"[SaveManager] Skipping instantiation of UISavePanel from saved uiStack: {uiPath}");
                            ResourceManager.Instance.ReleaseInstance(go);
                        }
                        else
                        {
                            // 先 parent 到正确 Canvas，再显示
                            Canvas canvasParent = uiManager.GetCanvas(panel.canvasDefine);
                            if (canvasParent != null)
                            {
                                go.transform.SetParent(canvasParent.transform, false);
                            }
                            uiManager.ShowUIPanel(panel);
                        }
                    }
                    loadedCount++;
                });
            }
            yield return new WaitUntil(() => loadedCount >= (saveData.uiStack?.Count ?? 0));
        }
*/
        GameEventSystem.Trigger(GameEventType.TransitionComplete);

        yield return transitionManager?.StartFade(0f);
        
         if (player != null)
        {
            player.isControllable = true;
            player.isInvincible = false;
        }
        
         if (uiManager != null)
        {
            uiManager.EnableUI();
        }

            callback?.Invoke();
            isLoading = false;
        }
        finally
        {
            isLoading = false;
            // 确保隐藏全局输入遮罩（如果存在）
            try
            {
                UIManager.Instance?.HideInputBlocker();
            }
            catch (Exception) { }
        }
    }

    private string GetSaveFilePath(int slot)
    {
        return $"{Application.persistentDataPath}/{SavePath}save_slot_{slot}{SaveFileExtension}";
    }
}