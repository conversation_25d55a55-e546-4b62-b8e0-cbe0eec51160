using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public enum MarkType
{
    None,       // 无
    Safe,       // 安全
    Danger,     // 危险
    Error,      // 错误
    Important,  // 重要
    Start,      // 起点
    End,        // 终点
}

public class UILetter : UIPanel
{
    [SerializeField] GameObject markPanel;
    [SerializeField] GameObject letterPanel;
    [SerializeField] Button btnTip;
    [SerializeField] Button btnLetter;
    [SerializeField] Button btnSwitch;
    [SerializeField] UIMark markPrefab;
    [SerializeField] Transform transGrid;
    [SerializeField] Sprite tipNormal;
    [SerializeField] Sprite tipHighlight;
    [SerializeField] Sprite letterNormal;
    [SerializeField] Sprite letterHighlight;
    [SerializeField] Transform btnGroup;
    readonly List<UIMark> markList = new();
    UIMark curUIMark;

    public override void OnInit()
    {
        base.OnInit();

        btnTip.onClick.AddListener(OnTipClick);
        btnLetter.onClick.AddListener(OnLetterClick);
        btnSwitch.onClick.AddListener(OnSwitchClick);

        markPrefab.gameObject.SetActive(false);
        InitGrid();
        InitBtnGroup();
        OnTipClick();
    }

    public override void OnShow(object param = null)
    {
        base.OnShow(param);
    }

    public override void OnHide()
    {
        base.OnHide();
    }

    void OnTipClick()
    {
        markPanel.SetActive(true);
        letterPanel.SetActive(false);

        btnTip.image.sprite = tipHighlight;
        btnTip.image.SetNativeSize();

        btnLetter.image.sprite = letterNormal;
        btnLetter.image.SetNativeSize();
    }

    void OnLetterClick()
    {
        markPanel.SetActive(false);
        letterPanel.SetActive(true);

        btnTip.image.sprite = tipNormal;
        btnTip.image.SetNativeSize();

        btnLetter.image.sprite = letterHighlight;
        btnLetter.image.SetNativeSize();
    }

    void OnSwitchClick()
    {
        foreach (UIMark item in markList)
        {
            item.markState = item.markState == MarkState.User ? MarkState.System : MarkState.User;
            item.Refresh();
        }
    }

    void InitGrid()
    {
        int count = RoomManager.Instance.MarkDatas.Count;
        for (int i = 0; i < count; i++)
        {
            UIMark item = Instantiate(markPrefab, transGrid);
            item.gameObject.SetActive(true);
            item.markData = RoomManager.Instance.MarkDatas[i];
            item.systemMarkData = RoomManager.Instance.SystemMarkDatas[i];
            item.Refresh();
            item.callback = () =>
            {
                curUIMark = item;
                btnGroup.transform.position = item.transform.position;
                RectTransform rect = btnGroup.GetComponent<RectTransform>();
                rect.anchoredPosition += new Vector2(0, -90);
                btnGroup.gameObject.SetActive(true);
            };
            markList.Add(item);
        }
    }

    void InitBtnGroup()
    {
        foreach (Transform item in btnGroup)
        {
            Button btn = item.GetComponent<Button>();
            string index = item.gameObject.name.Substring(3, 1);
            int indexInt = int.Parse(index);
            btn.onClick.AddListener(() =>
            {
                OnBtnClick(indexInt);
            });
        }
    }
    
    void OnBtnClick(int index)
    {
        MarkType markType = (MarkType)index;
        curUIMark.markData.markType = markType;
        curUIMark.Refresh();
        btnGroup.gameObject.SetActive(false);
    }
}
