using System;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.ResourceProviders;
using UnityEngine.SceneManagement;

public class ResourceManager : Singleton<ResourceManager>
{
    private SceneInstance currentScene;  // 当前的关卡场景
    public SceneInstance CurrentScene => currentScene;

    /// <summary>
    /// 加载附加场景
    /// </summary>
    /// <param name="scenePath">场景路径</param>
    /// <param name="callback">回调</param>
    public void LoadSceneAdditive(string scenePath, Action<Scene> callback = null)
    {
        UnloadCurrentLevelScene(()=>
        {
            Addressables.LoadSceneAsync(scenePath, LoadSceneMode.Additive).Completed += (operation) =>
            {
                currentScene = operation.Result;
                callback?.Invoke(operation.Result.Scene);
            };
        });
    }

    /// <summary>
    /// 卸载当前的附加场景
    /// </summary>
    /// <param name="callback">回调</param>
    public void UnloadCurrentLevelScene(Action callback = null)
    {
        if (currentScene.Scene.isLoaded)
        {
            Addressables.UnloadSceneAsync(currentScene).Completed += (operation) =>
            {
                callback?.Invoke();
            };
        }
        else
        {
            callback?.Invoke();
        }
    }

    /// <summary>
    /// 实例化预制体
    /// </summary>
    /// <param name="path">预制体路径</param>
    /// <param name="parent">父物体</param>
    /// <param name="callback">回调</param>
    public void InstantiatePrefab(string path, Transform parent, Action<GameObject> callback)
    {
        Addressables.InstantiateAsync(path, parent).Completed += (operation) =>
        {
            callback?.Invoke(operation.Result);
        };
    }

    /// <summary>
    /// 释放实例
    /// </summary>
    /// <param name="instance">要释放的实例</param>
    public void ReleaseInstance(GameObject instance)
    {
        if (instance != null)
        {
            Addressables.ReleaseInstance(instance);
        }
    }
}
