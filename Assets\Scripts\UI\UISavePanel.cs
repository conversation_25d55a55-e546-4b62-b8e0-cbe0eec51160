using System;
using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.IO;

public class UISavePanel : UIPanel
{
    [Header("保存与加载按钮")]
    [SerializeField] private Button saveButton1, saveButton2, saveButton3;
    [SerializeField] private Button loadButton1, loadButton2, loadButton3;
    [SerializeField] private Button closeButton;

    [Header("槽位显示")]
    [SerializeField] private Text saveSlot1Text, saveSlot2Text, saveSlot3Text;
    [SerializeField] private RawImage saveSlot1Image, saveSlot2Image, saveSlot3Image;
    [SerializeField] private Texture2D defaultSlotTexture; // 空存档默认贴图

    [Header("加载中提示（可选）")]
    [SerializeField] private GameObject loadingSpinner;

    private bool isUpdating = false;
    private bool[] isSlotUpdating = new bool[3]; // 每个槽位的刷新锁
    private bool isProcessing = false; // 防止快速多次点击

    protected override void Awake()
    {
        base.Awake();
        canvasDefine = CanvasDefine.Popup;
    }

    private void Start()
    {
        // ---------------- 保存按钮 ----------------
        {
            int slot1 = 1;
            saveButton1.onClick.AddListener(() =>
            {
                if (isProcessing) return;
                SetInteractable(false);
                SaveManager.Instance.SaveGame(slot1, () =>
                {
                    RefreshSlot(slot1);
                    SetInteractable(true);
                });
            });

            int slot2 = 2;
            saveButton2.onClick.AddListener(() =>
            {
                if (isProcessing) return;
                SetInteractable(false);
                SaveManager.Instance.SaveGame(slot2, () =>
                {
                    RefreshSlot(slot2);
                    SetInteractable(true);
                });
            });

            int slot3 = 3;
            saveButton3.onClick.AddListener(() =>
            {
                if (isProcessing) return;
                SetInteractable(false);
                SaveManager.Instance.SaveGame(slot3, () =>
                {
                    RefreshSlot(slot3);
                    SetInteractable(true);
                });
            });
        }

        // ---------------- 加载按钮 ----------------
        {
            int slot1 = 1;
            loadButton1.onClick.AddListener(() => OnLoadButtonClicked(slot1));

            int slot2 = 2;
            loadButton2.onClick.AddListener(() => OnLoadButtonClicked(slot2));

            int slot3 = 3;
            loadButton3.onClick.AddListener(() => OnLoadButtonClicked(slot3));
        }

        // 关闭按钮
        closeButton.onClick.AddListener(() => UIManager.Instance.HideTopUI());
    }

    public override void OnShow(object param = null)
    {
        if (isUpdating) return;
        Time.timeScale = 0f;
        gameObject.SetActive(true);
        // Load all slots synchronously within the same frame to avoid white placeholder images
        UpdateAllSlotsImmediate();
    }

    public override void OnHide()
    {
        Time.timeScale = 1f;
        gameObject.SetActive(false);
    }

    void SetInteractable(bool enable)
    {
        isProcessing = !enable;
        if (saveButton1 != null) saveButton1.interactable = enable;
        if (saveButton2 != null) saveButton2.interactable = enable;
        if (saveButton3 != null) saveButton3.interactable = enable;
        if (loadButton1 != null) loadButton1.interactable = enable;
        if (loadButton2 != null) loadButton2.interactable = enable;
        if (loadButton3 != null) loadButton3.interactable = enable;
        if (closeButton != null) closeButton.interactable = enable;
        if (loadingSpinner != null) loadingSpinner.SetActive(!enable);
    }

    void OnLoadButtonClicked(int slot)
    {
        if (isProcessing) return;
        // 锁定所有按钮并显示遮罩，防止玩家在加载期间交互
        SetInteractable(false);
        UIManager.Instance.ShowInputBlocker();

        // 立即关闭当前存档面板（只关闭本面板）
        UIManager.Instance.HideUI<UISavePanel>();

        // 后台开始加载；回调中会移除遮罩并解锁
        SaveManager.Instance.LoadGame(slot, () =>
        {
            // 尝试隐藏遮罩并解锁（SaveManager finally 也会尝试隐藏）
            UIManager.Instance.HideInputBlocker();
            SetInteractable(true);
            // 确保存档面板被关闭
            UIManager.Instance.HideUI<UISavePanel>();
        });
    }

    // ---------------- 公共刷新方法 ----------------

    public void RefreshSlot(int slot)
    {
        int capturedSlot = slot; // 固定捕获
        // perform immediate refresh to ensure image appears in same frame
        UpdateSingleSlotImmediate(capturedSlot);
    }

    // ---------------- 更新协程 ----------------

    private IEnumerator UpdateAllSlotsCoroutine()
    {
        isUpdating = true;
        if (loadingSpinner != null) loadingSpinner.SetActive(true);
        // kept for compatibility, but prefer immediate loading
        for (int i = 1; i <= 3; i++)
        {
            yield return UpdateSingleSlotCoroutine(i);
        }

        if (loadingSpinner != null) loadingSpinner.SetActive(false);
        isUpdating = false;
    }

    private IEnumerator UpdateSingleSlotCoroutine(int slot)
    {
        int index = slot - 1; // 对应 isSlotUpdating 索引
        if (isSlotUpdating[index]) yield break; // 锁定，避免重复刷新
        isSlotUpdating[index] = true;

        string path = $"{Application.persistentDataPath}/Saves/save_slot_{slot}.sav";
        Text slotText = slot == 1 ? saveSlot1Text : slot == 2 ? saveSlot2Text : saveSlot3Text;
        RawImage slotImage = slot == 1 ? saveSlot1Image : slot == 2 ? saveSlot2Image : saveSlot3Image;

        if (File.Exists(path))
        {
            string json = File.ReadAllText(path);
            SaveData data = JsonUtility.FromJson<SaveData>(json);

            string roomPart = string.IsNullOrEmpty(data.currentRoomTypeName) ? "" : $"位于{data.currentRoomTypeName}";
            slotText.text = $"{roomPart}{(string.IsNullOrEmpty(data.saveTime) ? "(未知时间)" : data.saveTime)}";

            string imagePath = path + ".png";
            if (slotImage != null)
            {
                if (File.Exists(imagePath))
                {
                    byte[] bytes = File.ReadAllBytes(imagePath);
                    Texture2D tex = new Texture2D(2, 2);
                    tex.LoadImage(bytes);
                    slotImage.texture = tex;
                }
                else
                {
                        slotImage.texture = defaultSlotTexture; // 使用默认空槽图片
                }
            }
        }
        else
        {
            slotText.text = $"槽位 {slot}: 空";
            if (slotImage != null)
                slotImage.texture = defaultSlotTexture; // 空槽显示默认贴图
        }

        isSlotUpdating[index] = false;
        yield return null;
    }

    // Immediate (synchronous) versions to ensure rendering within same frame
    private void UpdateAllSlotsImmediate()
    {
        isUpdating = true;
        if (loadingSpinner != null) loadingSpinner.SetActive(true);

        for (int i = 1; i <= 3; i++)
        {
            UpdateSingleSlotImmediate(i);
        }

        if (loadingSpinner != null) loadingSpinner.SetActive(false);
        isUpdating = false;
    }

    private void UpdateSingleSlotImmediate(int slot)
    {
        int index = slot - 1; // 对应 isSlotUpdating 索引
        if (isSlotUpdating[index]) return; // 锁定，避免重复刷新
        isSlotUpdating[index] = true;

        string path = $"{Application.persistentDataPath}/Saves/save_slot_{slot}.sav";
        Text slotText = slot == 1 ? saveSlot1Text : slot == 2 ? saveSlot2Text : saveSlot3Text;
        RawImage slotImage = slot == 1 ? saveSlot1Image : slot == 2 ? saveSlot2Image : saveSlot3Image;

        if (File.Exists(path))
        {
            string json = File.ReadAllText(path);
            SaveData data = JsonUtility.FromJson<SaveData>(json);

            if (slotText != null)
            {
                // 获取房间部分
                string roomPart = string.IsNullOrEmpty(data.currentRoomTypeName) ? "" : $"位于{data.currentRoomTypeName}";

                // 获取保存时间，并分割为日期和时间
                string saveTime = string.IsNullOrEmpty(data.saveTime) ? "(未知时间)" : data.saveTime;

                string datePart = saveTime;
                string timePart = "";

                string[] timeParts = saveTime.Split(new char[] { ' ' }, 2); 
                if (timeParts.Length == 2)
                {
                    datePart = timeParts[0]; // 日期部分
                    timePart = timeParts[1]; // 时间部分
                }

                // 构造最终文本，使用 \n 进行分行
                slotText.text = $"{roomPart}\n\n{datePart}\n{timePart}";
            }

            string imagePath = path + ".png";
            if (slotImage != null)
            {
                if (File.Exists(imagePath))
                {
                    try
                    {
                        byte[] bytes = File.ReadAllBytes(imagePath);
                        Texture2D tex = new Texture2D(2, 2);
                        tex.LoadImage(bytes);
                        slotImage.texture = tex;
                    }
                    catch (Exception e)
                    {
                        Debug.LogWarning($"UISavePanel: failed to load image for slot {slot}: {e.Message}");
                            slotImage.texture = defaultSlotTexture; // 加载失败时使用默认图片
                    }
                }
                else
                {
                        slotImage.texture = defaultSlotTexture; // 没有截图时使用默认空槽图片
                }
            }
        }
        else
        {
            if (slotText != null) slotText.text = $"槽位 {slot}: 空";
                if (slotImage != null) slotImage.texture = defaultSlotTexture; // 空槽显示默认贴图
        }

        isSlotUpdating[index] = false;
    }

    private void OnLoadComplete()
    {
        UIManager.Instance.HideTopUI();
    }
}
