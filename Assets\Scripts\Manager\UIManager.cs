﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public enum CanvasDefine
{
    Normal,   // 普通
    Popup,    // 弹窗
    Tips,     // 提示
    Loading,  // 加载
}

public class UIManager : SingletonMono<UIManager>
{
    public Canvas normalCanvas;
    public Canvas popupCanvas;
    public Canvas tipsCanvas;
    public Canvas loadingCanvas;
    public bool canOpenUI = true;

    public int orderSpace = 10;

    readonly Stack<UIPanel> uiStack = new();

    // 记录每个 Canvas 的当前最高层级
    readonly Dictionary<CanvasDefine, int> canvasCurrentLayers = new();

    protected override void Awake()
    {
        base.Awake();
        InitializeCanvasLayers();
        // 订阅传送相关事件，传送期间禁止打开 UI
        GameEventSystem.AddListener(GameEventType.TeleportStart, OnTeleportStart);
        GameEventSystem.AddListener(GameEventType.TeleportEnd, OnTeleportEnd);
        // 也在全局转场完成时解除阻塞（兼容跨场景传送）
        GameEventSystem.AddListener(GameEventType.TransitionComplete, OnTeleportEnd);
    }

    void OnDestroy()
    {
        try
        {
            GameEventSystem.RemoveListener(GameEventType.TeleportStart, OnTeleportStart);
            GameEventSystem.RemoveListener(GameEventType.TeleportEnd, OnTeleportEnd);
            GameEventSystem.RemoveListener(GameEventType.TransitionComplete, OnTeleportEnd);
        }
        catch { }
    }

    void InitializeCanvasLayers()
    {
        // 初始化每个 Canvas 的基础层级
        canvasCurrentLayers[CanvasDefine.Normal] = normalCanvas.sortingOrder;
        canvasCurrentLayers[CanvasDefine.Popup] = popupCanvas.sortingOrder;
        canvasCurrentLayers[CanvasDefine.Tips] = tipsCanvas.sortingOrder;
        canvasCurrentLayers[CanvasDefine.Loading] = loadingCanvas.sortingOrder;
    }

    public void ShowUI<T>(object param = null) where T : UIPanel
    {
        // Block opening when UI disabled, when an input blocker exists (covers keyboardless input),
        // or when a global transition/fade is in progress.
        if (!UIManager.Instance.CanOpenUI() || inputBlocker != null || (TransitionManager.Instance != null && TransitionManager.Instance.IsFading))
        {
            Debug.LogWarning("[UIManager] ShowUI blocked because UI disabled, input blocked, or transition in progress.");
            return;
        }
        // 调试：如果有人调用 ShowUI<UISavePanel>，打印调用堆栈以定位调用源
        if (typeof(T).Name == "UISavePanel")
        {
            try
            {
                var st = new System.Diagnostics.StackTrace(1, true);
                Debug.LogWarning($"[UIManager] ShowUI<UISavePanel> called. Stack:\n{st}");
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[UIManager] Failed to capture ShowUI stack: {e.Message}");
            }
        }

        // 检查是否已经存在该类型的 UI
        T existingPanel = GetExistingUI<T>();
        if (existingPanel != null)
        {
            // 如果已存在，重新显示并传递参数
            ShowUIPanel(existingPanel, param);
            return;
        }

        string panelName = typeof(T).Name;
        string path = $"Assets/Prefabs/UI/{panelName}.prefab";

        ResourceManager.Instance.InstantiatePrefab(path, transform, (go) =>
        {
            if (go == null)
            {
                Debug.LogError($"Failed to instantiate UI prefab: {panelName}");
                return;
            }

            if (!go.TryGetComponent<T>(out var uiPanel))
            {
                Debug.LogError($"UI prefab does not contain {panelName} component");
                ResourceManager.Instance.ReleaseInstance(go);
                return;
            }

            Canvas canvasParent = GetCanvas(uiPanel.canvasDefine);
            go.transform.SetParent(canvasParent.transform, false);

            ShowUIPanel(uiPanel, param);
        });
    }

    public Canvas GetCanvas(CanvasDefine canvasDefine)
    {
        return canvasDefine switch
        {
            CanvasDefine.Normal => normalCanvas,
            CanvasDefine.Popup => popupCanvas,
            CanvasDefine.Tips => tipsCanvas,
            CanvasDefine.Loading => loadingCanvas,
            _ => normalCanvas
        };
    }

    int GetCanvasLayerCount(CanvasDefine canvasDefine)
    {
        int count = 0;
        foreach (UIPanel panel in uiStack)
        {
            if (panel.canvasDefine == canvasDefine)
                count++;
        }
        return count;
    }

    int GetNextSortingOrder(CanvasDefine canvasDefine)
    {
        // 重新计算该 Canvas 的实际最高层级
        RecalculateCanvasLevel(canvasDefine);

        // 在当前最高层级基础上增加
        canvasCurrentLayers[canvasDefine] += orderSpace;
        return canvasCurrentLayers[canvasDefine];
    }

    void RecalculateCanvasLevel(CanvasDefine canvasDefine)
    {
        Canvas targetCanvas = GetCanvas(canvasDefine);
        int maxSortingOrder = targetCanvas.sortingOrder;

        // 查找栈中该 Canvas 的最高排序顺序
        foreach (UIPanel panel in uiStack)
        {
            if (panel.canvasDefine == canvasDefine && panel.Canvas != null)
            {
                maxSortingOrder = Math.Max(maxSortingOrder, panel.Canvas.sortingOrder);
            }
        }

        canvasCurrentLayers[canvasDefine] = maxSortingOrder;
    }

    void SwapSortingOrder(UIPanel panelToShow, UIPanel panelToPause)
    {
        // 获取两个界面的当前排序顺序
        int showPanelOrder = panelToShow.Canvas.sortingOrder;
        int pausePanelOrder = panelToPause.Canvas.sortingOrder;

        // 交换排序顺序
        panelToShow.SetSortingOrder(pausePanelOrder);
        panelToPause.SetSortingOrder(showPanelOrder);

        // 暂停原来的顶层界面
        panelToPause.OnPause();
    }

    void ReleaseSortingOrder(CanvasDefine canvasDefine)
    {
        // 重新计算该 Canvas 的最高层级
        RecalculateCanvasLevel(canvasDefine);
    }

    public T GetExistingUI<T>() where T : UIPanel
    {
        foreach (UIPanel panel in uiStack)
        {
            if (panel is T existingPanel)
            {
                return existingPanel;
            }
        }
        return null;
    }

    UIPanel GetTopMostPanel()
    {
        if (uiStack.Count == 0) return null;

        UIPanel topMostPanel = null;
        int highestSortingOrder = int.MinValue;

        foreach (UIPanel panel in uiStack)
        {
            if (panel.Canvas != null && panel.Canvas.sortingOrder > highestSortingOrder)
            {
                highestSortingOrder = panel.Canvas.sortingOrder;
                topMostPanel = panel;
            }
        }

        return topMostPanel;
    }

    UIPanel GetTopMostPanelInCanvas(CanvasDefine canvasDefine)
    {
        UIPanel topMostPanel = null;
        int highestSortingOrder = int.MinValue;

        foreach (UIPanel panel in uiStack)
        {
            if (panel.canvasDefine == canvasDefine &&
                panel.Canvas != null &&
                panel.Canvas.sortingOrder > highestSortingOrder)
            {
                highestSortingOrder = panel.Canvas.sortingOrder;
                topMostPanel = panel;
            }
        }

        return topMostPanel;
    }

    void RemovePanelFromStack(UIPanel targetPanel)
    {
        Stack<UIPanel> tempStack = new();

        // 将栈中的元素临时存储，跳过目标面板
        while (uiStack.Count > 0)
        {
            UIPanel panel = uiStack.Pop();
            if (panel != targetPanel)
            {
                tempStack.Push(panel);
            }
        }

        // 将其他面板放回栈中
        while (tempStack.Count > 0)
        {
            uiStack.Push(tempStack.Pop());
        }
    }

    public void ShowUIPanel(UIPanel panel, object param = null)
    {
        bool isReusing = uiStack.Contains(panel);

        // 如果面板已经在栈中，先移除它
        if (isReusing)
        {
            RemovePanelFromStack(panel);
        }

        // 暂停当前同一 Canvas 上层级最高的界面
        UIPanel currentTopPanel = GetTopMostPanelInCanvas(panel.canvasDefine);
        if (currentTopPanel != null && currentTopPanel != panel)
        {
            if (isReusing)
            {
                // 重用界面时，交换层级
                SwapSortingOrder(panel, currentTopPanel);
            }
            else
            {
                // 新界面时，暂停当前顶层界面并分配新层级
                currentTopPanel.OnPause();
                int sortingOrder = GetNextSortingOrder(panel.canvasDefine);
                panel.SetSortingOrder(sortingOrder);
            }
        }
        else
        {
            // 没有其他界面时，正常分配层级
            int sortingOrder = GetNextSortingOrder(panel.canvasDefine);
            panel.SetSortingOrder(sortingOrder);
        }
        panel.OnShow(param);
        uiStack.Push(panel);
    }

    public void HideTopUI()
    {
        if (uiStack.Count == 0) return;

        // 找到 Canvas 层级最高的界面
        UIPanel topMostPanel = GetTopMostPanel();
        if (topMostPanel == null) return;

        // 记录 Canvas 类型和排序顺序（在销毁前）
        CanvasDefine canvasDefine = topMostPanel.canvasDefine;
        int sortingOrder = topMostPanel.Canvas != null ? topMostPanel.Canvas.sortingOrder : 0;

        // 从栈中移除该界面
        RemovePanelFromStack(topMostPanel);

        topMostPanel.OnHide();

        // 释放实例
        ResourceManager.Instance.ReleaseInstance(topMostPanel.gameObject);

        // 释放层级（在销毁后重新计算）
        ReleaseSortingOrder(canvasDefine);

        // 恢复同一 Canvas 上的下一个最高层界面
        UIPanel nextTopPanel = GetTopMostPanelInCanvas(canvasDefine);
        if (nextTopPanel != null)
        {
            nextTopPanel.OnResume();
        }
    }

    public void HideUI<T>() where T : UIPanel
    {
        // 查找指定类型的UI面板
        UIPanel targetPanel = null;
        foreach (UIPanel panel in uiStack)
        {
            if (panel is T)
            {
                targetPanel = panel;
                break;
            }
        }

        if (targetPanel == null) return;

        // 关闭目标面板及其上层的所有面板
        Stack<UIPanel> tempStack = new();
        while (uiStack.Count > 0)
        {
            UIPanel panel = uiStack.Pop();

            // 记录 Canvas 信息（在销毁前）
            CanvasDefine canvasDefine = panel.canvasDefine;
            int sortingOrder = panel.Canvas != null ? panel.Canvas.sortingOrder : 0;

            panel.OnHide();
            ResourceManager.Instance.ReleaseInstance(panel.gameObject);

            // 释放层级（在销毁后）
            ReleaseSortingOrder(canvasDefine);

            if (panel == targetPanel) break;
        }

        // 恢复下层界面
        if (uiStack.Count > 0)
        {
            UIPanel topPanel = uiStack.Peek();
            topPanel.OnResume();
        }
    }

    public Stack<UIPanel> GetUIStack()
    {
        return new Stack<UIPanel>(uiStack); // 返回副本以保护原始栈
    }

    public void ClearUIStack()
    {
        while (uiStack.Count > 0)
        {
            var panel = uiStack.Pop();
            panel.OnHide();
            ResourceManager.Instance.ReleaseInstance(panel.gameObject);
        }
    }

    // 关闭并释放指定 Canvas 下的所有面板（例如只关闭 Popup 类型的临时弹窗）
    public void ClosePanelsInCanvas(CanvasDefine canvasDefine)
    {
        Stack<UIPanel> tempStack = new();

        // 移除并处理匹配的面板，保留其他面板
        while (uiStack.Count > 0)
        {
            UIPanel panel = uiStack.Pop();
            if (panel.canvasDefine == canvasDefine)
            {
                panel.OnHide();
                ResourceManager.Instance.ReleaseInstance(panel.gameObject);
                // 释放层级之后在循环结束时重新计算
            }
            else
            {
                tempStack.Push(panel);
            }
        }

        // 把保留的面板放回栈中（保持原来的顺序）
        while (tempStack.Count > 0)
        {
            uiStack.Push(tempStack.Pop());
        }

        // 重新计算该 Canvas 的层级
        ReleaseSortingOrder(canvasDefine);
    }

    // 输入阻挡遮罩（用于加载时锁定所有点击）
    private GameObject inputBlocker;

    public void ShowInputBlocker()
    {
        if (inputBlocker != null) return;

        Canvas targetCanvas = loadingCanvas != null ? loadingCanvas : normalCanvas;
        if (targetCanvas == null)
        {
            Debug.LogWarning("UIManager.ShowInputBlocker: no target canvas available");
            return;
        }

        inputBlocker = new GameObject("InputBlocker");
        inputBlocker.transform.SetParent(targetCanvas.transform, false);
        var img = inputBlocker.AddComponent<Image>();
        img.color = new Color(0f, 0f, 0f, 0f); // 透明遮罩但拦截射线
        img.raycastTarget = true;
        var rt = inputBlocker.GetComponent<RectTransform>();
        rt.anchorMin = Vector2.zero;
        rt.anchorMax = Vector2.one;
        rt.offsetMin = Vector2.zero;
        rt.offsetMax = Vector2.zero;
        // 放到最顶层
        inputBlocker.transform.SetAsLastSibling();
    }

    void OnTeleportStart()
    {
        Debug.Log("[UIManager] OnTeleportStart: disabling UI and showing input blocker.");
        // 禁止打开 UI 并显示输入阻塞遮罩
        DisableUI();
        ShowInputBlocker();
    }

    void OnTeleportEnd()
    {
        Debug.Log("[UIManager] OnTeleportEnd: hiding input blocker and enabling UI.");
        // 解除阻塞并隐藏遮罩
        HideInputBlocker();
        EnableUI();
    }

    public void HideInputBlocker()
    {
        if (inputBlocker == null) return;
        UnityEngine.Object.Destroy(inputBlocker);
        inputBlocker = null;
    }

    public bool CanOpenUI()
    {
        return canOpenUI;
    }
    public void EnableUI()
    {
        canOpenUI = true;
    }

    public void DisableUI()
    {
        canOpenUI = false;
    }
}