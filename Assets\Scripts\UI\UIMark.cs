using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public enum MarkState
{
    User,
    System,
}

public class UIMark : MonoBehaviour
{
    [SerializeField] Button btn;
    [SerializeField] Image img;
    [SerializeField] Image imgContent;
    [SerializeField] Text txt;
    [SerializeField] List<Color> colors;
    [SerializeField] List<Sprite> markSprites;
    [SerializeField] List<Sprite> markSpritesMini;
    [SerializeField] Sprite startSprite;
    [SerializeField] Sprite endSprite;
    public MarkState markState;
    public MarkData markData;
    public MarkData systemMarkData;
    public Action callback;
    public bool isMini;

    void Awake()
    {
        btn.onClick.AddListener(OnButtonClick);
    }

    void OnButtonClick()
    {
        if (markState != MarkState.User) return;
        callback?.Invoke();
    }

    public void Refresh()
    {
        MarkType curMarkType = MarkType.None;
        if (markState == MarkState.User)
        {
            curMarkType = markData.markType;
        }
        else if (markState == MarkState.System)
        {
            curMarkType = systemMarkData.markType;
        }

        img.color = new Color(1, 1, 1, 1);
        imgContent.color = new Color(1, 1, 1, 0);

        switch (curMarkType)
        {
            case MarkType.None:
                txt.text = "无";
                img.color = new Color(1, 1, 1, 0);
                break;
            case MarkType.Safe:
                txt.text = "安全";
                img.sprite = isMini ? markSpritesMini[0] : markSprites[0];
                break;
            case MarkType.Danger:
                txt.text = "危险";
                img.sprite = isMini ? markSpritesMini[1] : markSprites[1];
                break;
            case MarkType.Error:
                txt.text = "错误";
                img.sprite = isMini ? markSpritesMini[2] : markSprites[2];
                break;
            case MarkType.Important:
                txt.text = "重要";
                img.sprite = isMini ? markSpritesMini[3] : markSprites[3];
                break;
            case MarkType.Start:
                txt.text = "起点";
                img.color = new Color(1, 1, 1, 0);
                imgContent.sprite = startSprite;
                imgContent.color = new Color(1, 1, 1, 1);
                break;
            case MarkType.End:
                txt.text = "终点";
                img.color = new Color(1, 1, 1, 0);
                imgContent.sprite = endSprite;
                imgContent.color = new Color(1, 1, 1, 1);
                break;
        }
    }
}
