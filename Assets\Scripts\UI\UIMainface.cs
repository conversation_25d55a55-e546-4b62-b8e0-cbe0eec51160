using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class UIMainface : UIPanel
{
    [SerializeField] GameObject goSan;
    [SerializeField] Image imgSan;
    [SerializeField] Text txtSan;
    [SerializeField] Button btnLetter;
    [SerializeField] Sprite letterOpen;
    [SerializeField] Sprite letterClose;
    [SerializeField] Button btnRoomKey;
    [SerializeField] Button btnBuffShield;
    [SerializeField] Button btnBuffDoubleStep;
    [SerializeField] Button btnSetting;
    [SerializeField] Button btnCopy;
    [SerializeField] Image miniMap;
    [SerializeField] UIMark markPrefab;
    [SerializeField] Transform transGrid;

    public override void OnInit()
    {
        base.OnInit();
        btnLetter.onClick.AddListener(OnLetterClick);
        btnCopy.onClick.AddListener(OnCopyClick);
        InitGrid();
    }

    public override void OnShow(object param = null)
    {
        base.OnShow(param);
        GameEventSystem.AddListener<RoomType>(GameEventType.EnterRoom, OnEnterRoom);
        GameEventSystem.AddListener<string>(GameEventType.UIClosed, OnUIClosed);
    }

    public override void OnHide()
    {
        base.OnHide();
        GameEventSystem.RemoveListener<RoomType>(GameEventType.EnterRoom, OnEnterRoom);
        GameEventSystem.RemoveListener<string>(GameEventType.UIClosed, OnUIClosed);
    }

    void OnEnterRoom(RoomType roomType)
    {
        switch (roomType)
        {
            case RoomType.Normal:
                break;
            case RoomType.Dead:
                break;
            case RoomType.Basement:
                break;
            case RoomType.Luxury:
                break;
        }
    }

    void OnUIClosed(string uiName)
    {
        if (uiName == "UILetter")
        {
            btnLetter.image.sprite = letterClose;
            btnLetter.image.SetNativeSize();
        }
    }

    void OnLetterClick()
    {
        UIManager.Instance.ShowUI<UILetter>();
        btnLetter.image.sprite = letterOpen;
        btnLetter.image.SetNativeSize();
    }

    void InitGrid()
    {
        int count = RoomManager.Instance.MarkDatas.Count;
        for (int i = 0; i < count; i++)
        {
            UIMark item = Instantiate(markPrefab, transGrid);
            item.gameObject.SetActive(true);
            item.markData = RoomManager.Instance.MarkDatas[i];
            item.systemMarkData = RoomManager.Instance.SystemMarkDatas[i];
            item.markState = MarkState.System;
            item.isMini = true;
            item.Refresh();
        }
    }
        void OnCopyClick()
    {
        UIManager.Instance.ShowUI<UISavePanel>();
    }
}
