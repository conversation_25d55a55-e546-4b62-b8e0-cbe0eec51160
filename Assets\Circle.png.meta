fileFormatVersion: 2
guid: f7500e7e307aab94fb30e355bb576204
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -2413806693520163455
    second: Circle
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 256
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: 0
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Circle
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline:
      - - {x: 33, y: 128}
        - {x: -33, y: 128}
        - {x: -95, y: 95}
        - {x: -128, y: 33}
        - {x: -128, y: -33}
        - {x: -95, y: -95}
        - {x: -33, y: -128}
        - {x: 33, y: -128}
        - {x: 95, y: -95}
        - {x: 128, y: -33}
        - {x: 128, y: 33}
        - {x: 95, y: 95}
      physicsShape:
      - - {x: 0, y: 128}
        - {x: -39, y: 121}
        - {x: -75, y: 103}
        - {x: -103, y: 75}
        - {x: -121, y: 39}
        - {x: -128, y: 0}
        - {x: -121, y: -39}
        - {x: -103, y: -75}
        - {x: -75, y: -103}
        - {x: -39, y: -121}
        - {x: 0, y: -128}
        - {x: 39, y: -121}
        - {x: 75, y: -103}
        - {x: 103, y: -75}
        - {x: 121, y: -39}
        - {x: 128, y: 0}
        - {x: 121, y: 39}
        - {x: 103, y: 75}
        - {x: 75, y: 103}
        - {x: 39, y: 121}
      tessellationDetail: 0
      bones: []
      spriteID: 18d3544e99f608ed0800000000000000
      internalID: -2413806693520163455
      vertices: []
      indices: 
      edges: []
      weights: []
    outline:
    - - {x: 33, y: 128}
      - {x: -33, y: 128}
      - {x: -95, y: 95}
      - {x: -128, y: 33}
      - {x: -128, y: -33}
      - {x: -95, y: -95}
      - {x: -33, y: -128}
      - {x: 33, y: -128}
      - {x: 95, y: -95}
      - {x: 128, y: -33}
      - {x: 128, y: 33}
      - {x: 95, y: 95}
    physicsShape:
    - - {x: 0, y: 128}
      - {x: -39, y: 121}
      - {x: -75, y: 103}
      - {x: -103, y: 75}
      - {x: -121, y: 39}
      - {x: -128, y: 0}
      - {x: -121, y: -39}
      - {x: -103, y: -75}
      - {x: -75, y: -103}
      - {x: -39, y: -121}
      - {x: 0, y: -128}
      - {x: 39, y: -121}
      - {x: 75, y: -103}
      - {x: 103, y: -75}
      - {x: 121, y: -39}
      - {x: 128, y: 0}
      - {x: 121, y: 39}
      - {x: 103, y: 75}
      - {x: 75, y: 103}
      - {x: 39, y: 121}
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Circle: -2413806693520163455
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
