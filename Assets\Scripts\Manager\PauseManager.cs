﻿using UnityEngine;

public class PauseInput : MonoBehaviour
{
    void Update()
    {
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            UIPause pausePanel = UIManager.Instance.GetExistingUI<UIPause>();
            if (pausePanel != null && pausePanel.CanvasGroup.alpha > 0f)
            {
                UIManager.Instance.HideUI<UIPause>();
            }
            else
            {
                UIManager.Instance.ShowUI<UIPause>();
            }
        }
    }
}
