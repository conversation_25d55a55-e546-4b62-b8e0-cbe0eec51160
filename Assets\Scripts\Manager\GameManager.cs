using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

public class GameManager : SingletonMono<GameManager>
{
    public string firstScenePath = "Assets/Scenes/Level1.unity";
    Dictionary<string, GameObject> teleportPoints = new();
    public string TargetSpawnPointName { get; set; }

    void Start()
    {
        // 加载第一关
        ResourceManager.Instance.LoadSceneAdditive(firstScenePath, (scene) => 
        {
            if (scene.IsValid())
            {
                SceneManager.SetActiveScene(scene);
            }

            RegisterFirstSceneStartPoint();

            GameEventSystem.Trigger(GameEventType.SceneLoaded);
            GameEventSystem.Trigger(GameEventType.TransitionComplete);
            UIManager.Instance.ShowUI<UIMainface>();
        });
        
        GameEventSystem.AddListener(GameEventType.SceneLoaded, OnSceneLoaded);
    }

    void Update()
    {

    }

    void OnSceneLoaded()
    {
        if (!string.IsNullOrEmpty(TargetSpawnPointName))
        {
            GameObject spawnPoint = GetTeleportPoint(TargetSpawnPointName);
            if (spawnPoint != null)
            {
                PlayerController player = FindObjectOfType<PlayerController>();
                if (player != null)
                {
                    player.transform.position = spawnPoint.transform.position;
                }
            }
            TargetSpawnPointName = null;
        }
    }

    
    /// <summary>
    /// 注册第一关起点到 TransitionManager，用于重生回第一关
    /// </summary>
    void RegisterFirstSceneStartPoint()
    {
        GameObject start = GameObject.Find("PlayerSpawnPoint");
        if (start != null)
        {
            TransitionManager.Instance.SetFirstSceneStartPoint(start.transform);
            Debug.Log("[GameManager] 已注册第一关起点：" + start.name);
        }
        else
        {
            Debug.LogWarning("[GameManager] 未找到 PlayerSpawnPoint");
        }
    }

    public void RegisterTeleportPoint(string name, GameObject point)
    {
        teleportPoints[name] = point;
    }

    public void UnregisterTeleportPoint(string name)
    {
        teleportPoints.Remove(name);
    }

    public GameObject GetTeleportPoint(string name)
    {
        teleportPoints.TryGetValue(name, out GameObject point);
        return point;
    }
    
    void OnDestroy()
    {
        GameEventSystem.RemoveListener(GameEventType.SceneLoaded, OnSceneLoaded);
    }

    public Dictionary<string, GameObject> GetTeleportPoints()
    {
        return teleportPoints;
    }

    public void ClearTeleportPoints()
    {
        teleportPoints.Clear();
    }
}
