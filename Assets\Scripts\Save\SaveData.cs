using System;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public class SaveData
{
    //存档时间
    public string saveTime;
    
    // 玩家数据
    public Vector2 playerPosition;
    public int currentHealth;
    public float san;
    public bool hasShield;
    public bool hasKey;
    public bool hasLetter;
    public bool extraMove;
    public bool letterLost;
    public bool inBasement;

    // 场景数据
    public string currentScenePath;
    public string targetSpawnPointName;

    // 房间数据
    public int currentRoomRow;
    public int currentRoomCol;
    // 房间类型显示（例如：位于大厅）。保存为字符串，便于以后定制显示文本。
    public string currentRoomTypeName;
    public List<MarkData> markDatas;
    public List<MarkData> systemMarkDatas;

    // 传送点数据
    public Dictionary<string, Vector2> teleportPoints;

    // UI 数据
    //public List<string> uiStack; // 存储 UI 面板的 prefab 路径
}