using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
#if UNITY_EDITOR
using UnityEditor;
#endif

public enum RoomType
{
    Start,      // 起点房间
    Normal,     // 普通房间
    Dead,       // 死亡房间
    Basement,   // 地下室
    Luxury,     // 豪华套房
    End,        // 终点房间
    Bug,        // Bug 房间
    Extra,      // 额外房间
    Dining,     // 餐厅
    Dark,       // 暗室
    Key,        // 房卡房间
}

public class Room : MonoBehaviour
{
    public int row;                   // 行数
    public int col;                   // 列数
    public int index;                 // 索引
    public RoomType roomType;         // 房间类型
    public string scenePath;          // 场景路径
    public Transform spawnPoint;      // 出生点
    public Transform door;            // 传送门
    public bool enableGizmos;         // 是否显示线框

    Transform startRoom;
    Transform normalRoom;
    Transform deadRoom;
    Transform basementRoom;
    Transform luxuryRoom;
    Transform endRoom;
    Transform bugRoom;
    Transform extraRoom;
    Transform diningRoom;
    Transform darkRoom;
    Transform keyRoom;

    void Awake()
    {
        index = row * 5 + col;

        startRoom = transform.Find("Start");
        normalRoom = transform.Find("Normal");
        deadRoom = transform.Find("Dead");
        basementRoom = transform.Find("Basement");
        luxuryRoom = transform.Find("Luxury");
        endRoom = transform.Find("End");
        bugRoom = transform.Find("Bug");
        extraRoom = transform.Find("Extra");
        diningRoom = transform.Find("Dining");
        darkRoom = transform.Find("Dark");
        keyRoom = transform.Find("Key");

        if (startRoom != null)
            startRoom.gameObject.SetActive(roomType == RoomType.Start);
        if (normalRoom != null)
            normalRoom.gameObject.SetActive(roomType == RoomType.Normal);
        if (deadRoom != null)
            deadRoom.gameObject.SetActive(roomType == RoomType.Dead);
        if (basementRoom != null)
            basementRoom.gameObject.SetActive(roomType == RoomType.Basement);
        if (luxuryRoom != null)
            luxuryRoom.gameObject.SetActive(roomType == RoomType.Luxury);
        if (endRoom != null)
            endRoom.gameObject.SetActive(roomType == RoomType.End);
        if (bugRoom != null)
            bugRoom.gameObject.SetActive(roomType == RoomType.Bug);
        if (extraRoom != null)
            extraRoom.gameObject.SetActive(roomType == RoomType.Extra);
        if (diningRoom != null)
            diningRoom.gameObject.SetActive(roomType == RoomType.Dining);
        if (darkRoom != null)
            darkRoom.gameObject.SetActive(roomType == RoomType.Dark);
        if (keyRoom != null)
            keyRoom.gameObject.SetActive(roomType == RoomType.Key);

        if (roomType == RoomType.End)
        {
            Transform target = transform.Find("End/Portal/Portal_1");
            if (target != null)
            {
                if (target.TryGetComponent<Portal>(out var portal))
                {
                    portal.targetSceneAddress = scenePath;
                    portal.isDifferentScene = true;
                }
            }
        }
    }

    private void OnDrawGizmos()
    {
        if (!enableGizmos) return;

        Color baseColor = Color.white;   
        switch (roomType)
        {
            case RoomType.Start: 
                baseColor = Color.white; 
                break;
            case RoomType.Normal: 
                baseColor = Color.green; 
                break;
            case RoomType.Dead: 
                baseColor = Color.red; 
                break;
            case RoomType.Basement: 
                baseColor = Color.black; 
                break;
            case RoomType.Luxury:
                baseColor = Color.yellow;
                break;
            case RoomType.End: 
                baseColor = Color.blue;
                break;
            case RoomType.Bug:
                baseColor = Color.magenta;
                break;
            case RoomType.Extra:
                baseColor = Color.cyan;
                break;
            case RoomType.Dining:
                baseColor = Color.yellow;
                break;
            case RoomType.Dark:
                baseColor = Color.gray;
                break;
            case RoomType.Key:
                baseColor = Color.yellow;
                break;
        }

        
        // 半透明实心球
        Gizmos.color = new Color(baseColor.r, baseColor.g, baseColor.b, 0.4f);
        Gizmos.DrawSphere(transform.position, 20f);

        // 对比线框
        Gizmos.color = baseColor;
        Gizmos.DrawWireSphere(transform.position, 20f);

#if UNITY_EDITOR
        // 显示房间名字标签，右上方
        Handles.Label(transform.position + Vector3.up * 22f, roomType.ToString());
#endif
    }
}
